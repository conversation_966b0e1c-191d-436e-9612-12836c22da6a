# Dify完成消息API客户端

基于Dify completion-messages API的客户端实现，支持发送查询内容并获取AI生成的回答。现已集成到ChatService中，提供提示词优化功能。

## 功能特性

- 🚀 **同步HTTP调用**：使用OkHttp实现高性能的同步HTTP请求
- 📝 **完整的请求/响应模型**：类型安全的请求和响应数据结构
- 🛡️ **错误处理**：完善的异常处理和日志记录
- ⚙️ **配置化**：支持通过配置文件自定义API地址和密钥
- 🧪 **单元测试**：包含完整的单元测试覆盖
- ✨ **提示词优化**：专门的提示词优化接口，支持文本和图片输入，返回优化后的详细提示词

## 快速开始

### 1. 配置

在`application.yml`中添加Dify API配置：

```yaml
dify:
  completion-messages-url: "http://dify.weilitech.cn/v1/completion-messages"
  completion-messages-api-key: "your-api-key-here"
```

### 2. 使用客户端

#### 通过服务类使用（推荐）

```java
@Autowired
private DifyCompletionService difyCompletionService;

// 获取完整响应
DifyCompletionResponse response = difyCompletionService.requestCompletion(
    "风格化3D卡通画面，色彩明亮，皮克斯动画电影质感。我需要一个抖音投放的快节奏游戏广告...", 
    "user-123"
);

// 仅获取答案内容
String answer = difyCompletionService.requestCompletionAnswer(
    "风格化3D卡通画面，色彩明亮，皮克斯动画电影质感。我需要一个抖音投放的快节奏游戏广告...", 
    "user-123"
);
```

#### 直接使用客户端

```java
@Autowired
private DifyCompletionClient difyCompletionClient;

try {
    DifyCompletionResponse response = difyCompletionClient.requestCompletion(
        "你的查询内容", 
        "user-123"
    );
    System.out.println("回答: " + response.getAnswer());
} catch (IOException e) {
    log.error("API调用失败", e);
}
```

### 3. 使用提示词优化功能

```java
@Autowired
private ChatService chatService;

// 提示词优化（无图片）
PromptOptimizeRes result = chatService.optimizePrompt("生成一个游戏广告");
System.out.println("优化后的提示词: " + result.getOptimizedPrompt());

// 提示词优化（带图片）
List<String> imageUrls = Arrays.asList(
    "https://wlpaas.weilitech.cn/dify/prod/1927918453497950208/image/98e6e550a81441d58e42b6b68f8ba0e4.png"
);
PromptOptimizeRes imageResult = chatService.optimizePrompt(
    "根据图中的人物，创作一个故事",
    imageUrls
);
System.out.println("优化后的提示词: " + imageResult.getOptimizedPrompt());
```

### 4. 使用REST API

客户端还提供了REST API接口：

```bash
# 获取完整响应
curl -X POST "http://localhost:8080/api/dify/completion/request" \
  -d "query=你的查询内容" \
  -d "user=abc-123"

# 仅获取答案内容
curl -X POST "http://localhost:8080/api/dify/completion/answer" \
  -d "query=你的查询内容" \
  -d "user=abc-123"

# 提示词优化（集成在ChatController中）
# 无图片
curl -X POST "http://localhost:8080/api/chat/optimize-prompt" \
  -H "Content-Type: application/json" \
  -d '{"prompt":"生成一个游戏广告"}'

# 带图片
curl -X POST "http://localhost:8080/api/chat/optimize-prompt" \
  -H "Content-Type: application/json" \
  -d '{"prompt":"根据图中的人物，创作一个故事","imageUrls":["https://wlpaas.weilitech.cn/dify/prod/1927918453497950208/image/98e6e550a81441d58e42b6b68f8ba0e4.png"]}'
```

## API参考

### 基础API请求参数

| 参数 | 类型 | 必填 | 说明 | 示例 |
|------|------|------|------|------|
| query | String | 是 | 查询内容 | "风格化3D卡通画面..." |
| user | String | 是 | 用户标识 | "abc-123" |
| response_mode | String | 否 | 响应模式，默认"blocking" | "blocking" |

### 提示词优化API参数

| 参数 | 类型 | 必填 | 说明 | 示例 |
|------|------|------|------|------|
| prompt | String | 是 | 原始提示词 | "生成一个游戏广告" |
| imageUrls | List<String> | 否 | 图片URL集合 | ["https://example.com/image.png"] |

**注意：** 用户标识(user)会自动从UserContext中获取，无需手动传入。

### 基础API响应参数

| 参数 | 类型 | 说明 | 示例 |
|------|------|------|------|
| event | String | 事件类型 | "message" |
| task_id | String | 任务ID | "cf1063f6-858d-448b-a1d2-97b5b74cfd8a" |
| id | String | 消息ID | "618340e6-7b58-417d-9ba7-5f564a1c3dde" |
| message_id | String | 消息ID（与id相同） | "618340e6-7b58-417d-9ba7-5f564a1c3dde" |
| mode | String | 模式 | "completion" |
| answer | String | AI生成的回答内容 | "{\\"core_info\\": ...}" |
| created_at | Long | 创建时间戳 | 1754301483 |

### 提示词优化响应参数

| 参数 | 类型 | 说明 | 示例 |
|------|------|------|------|
| optimizedPrompt | String | 优化后的提示词 | "风格化3D卡通画面，色彩明亮..." |

## 配置说明

### DifyConfig配置项

| 配置项 | 默认值 | 说明 |
|--------|--------|------|
| dify.completion-messages-url | "http://dify.weilitech.cn/v1/completion-messages" | API地址 |
| dify.completion-messages-api-key | "app-default-completion-messages-key" | API密钥 |

### HTTP客户端配置

客户端使用OkHttp，具有以下特性：
- 连接超时：10秒
- 读取超时：30秒
- 写入超时：30秒
- 自动重试连接失败

## 错误处理

客户端提供完整的错误处理机制：

1. **网络错误**：抛出`IOException`
2. **API错误**：抛出`BizException`包含详细错误信息
3. **序列化错误**：抛出`BizException`
4. **业务逻辑错误**：服务层返回null，记录警告日志

## 示例响应

```json
{
  "event": "message",
  "task_id": "cf1063f6-858d-448b-a1d2-97b5b74cfd8a",
  "id": "618340e6-7b58-417d-9ba7-5f564a1c3dde",
  "message_id": "618340e6-7b58-417d-9ba7-5f564a1c3dde",
  "mode": "completion",
  "answer": "{\"core_info\": {\"topic_analysis\": \"游戏广告的核心在于展现其采集、建造、生存、对抗的玩法特色\",\"audience_insights\": \"目标受众为喜欢策略、生存、模拟建造类手机游戏的玩家\"}}",
  "created_at": 1754301483
}
```

## 注意事项

1. **API密钥安全**：请妥善保管API密钥，不要在代码中硬编码
2. **查询内容长度**：注意查询内容的长度限制
3. **并发控制**：注意API的并发调用限制
4. **错误重试**：建议实现适当的重试机制
5. **日志记录**：客户端会记录详细的请求和响应日志

## 依赖要求

- Java 17+
- Spring Boot 2.7+
- OkHttp 4.x
- Jackson 2.x
- Lombok

## 更新日志

### v1.0.0
- 初始版本发布
- 支持Dify completion-messages API调用
- 完整的错误处理和日志记录
- 单元测试覆盖
- REST API接口
