# Fal AI Lyria2 音乐生成客户端

基于 Fal AI Lyria2 API 的音乐生成客户端，支持通过文本描述生成高质量的音乐文件。

## 功能特性

- 🎵 **文本到音乐生成**：根据文本描述生成音乐
- 🔄 **同步/异步支持**：支持同步等待和异步处理
- 🎯 **确定性生成**：支持种子值确保结果可重现
- 🚫 **负面提示词**：支持排除不需要的音乐元素
- 📊 **任务状态跟踪**：实时查询生成进度
- ⚡ **自动重试**：内置重试机制提高成功率
- 🛡️ **错误处理**：完善的异常处理和日志记录
- 💾 **数据库记录**：完整记录生成历史和状态
- 📈 **统计分析**：支持用户生成统计和任务监控

## 配置

### 1. 添加 API Key

在配置文件中添加 Fal AI API Key：

```yaml
# application.yml
fal-ai:
  base-url: https://queue.fal.run
  api-keys:
    lyria2: your-fal-ai-api-key-here
```

### 2. 启用示例（可选）

如果要运行示例代码，添加以下配置：

```yaml
music:
  generation:
    example:
      enabled: true
```

## 使用方式

### 1. 服务层使用

```java
@Autowired
private MusicGenerationService musicGenerationService;

// 简单生成音乐
public void generateSimpleMusic() throws IOException {
    String prompt = "A peaceful piano melody with gentle rain sounds";
    Lyria2Output result = musicGenerationService.generateMusicByPrompt(prompt);
    
    System.out.println("音频URL: " + result.getAudioUrl());
    System.out.println("文件大小: " + result.getFormattedAudioFileSize());
}

// 带种子值生成
public void generateMusicWithSeed() throws IOException {
    String prompt = "Upbeat electronic dance music";
    Integer seed = 12345;
    Lyria2Output result = musicGenerationService.generateMusicWithSeed(prompt, seed);
    
    System.out.println("音频URL: " + result.getAudioUrl());
}

// 异步生成
public void generateMusicAsync() {
    String prompt = "Classical orchestral music";
    CompletableFuture<Lyria2Output> future = 
        musicGenerationService.generateMusicByPromptAsync(prompt);
    
    future.thenAccept(result -> {
        System.out.println("异步生成完成: " + result.getAudioUrl());
    });
}
```

### 2. 客户端直接使用

```java
@Autowired
private FalAiLyria2Client falAiLyria2Client;

public void useClientDirectly() throws IOException {
    // 创建输入参数
    Lyria2Input input = Lyria2Input.builder()
        .prompt("Ambient soundscape with nature sounds")
        .seed(54321)
        .negativePrompt("loud, aggressive")
        .build();
    
    // 同步生成
    Lyria2Output result = falAiLyria2Client.generateMusicSync(input);
    System.out.println("生成完成: " + result.getAudioUrl());
}
```

### 3. 分步骤处理

```java
public void stepByStepGeneration() throws IOException {
    Lyria2Input input = Lyria2Input.simple("Jazz music with saxophone");
    
    // 步骤1：提交请求
    FalQueueStatus status = musicGenerationService.submitMusicGenerationRequest(input);
    String requestId = status.getRequestId();
    
    // 步骤2：轮询状态
    while (!status.isCompleted()) {
        Thread.sleep(2000);
        status = musicGenerationService.getTaskStatus(requestId);
        System.out.println("状态: " + status.getStatusDescription());
    }
    
    // 步骤3：获取结果
    Lyria2Output result = musicGenerationService.getTaskResult(requestId);
    System.out.println("结果: " + result.getAudioUrl());
}
```

## REST API 接口

### 同步生成音乐

```http
POST /api/music/generate
Content-Type: application/json

{
  "prompt": "A peaceful piano melody with gentle rain sounds",
  "seed": 12345,
  "negativePrompt": "loud, aggressive"
}
```

### 简单生成（GET 请求）

```http
POST /api/music/generate/simple?prompt=Relaxing ambient music
```

### 异步生成

```http
POST /api/music/generate/async
Content-Type: application/json

{
  "prompt": "Electronic dance music with heavy bass"
}
```

### 查询任务状态

```http
GET /api/music/task/{requestId}/status
```

### 获取任务结果

```http
GET /api/music/task/{requestId}/result
```

### 等待任务完成

```http
GET /api/music/task/{requestId}/wait
```

### 取消任务

```http
DELETE /api/music/task/{requestId}
```

### 查询生成记录

```http
GET /api/music/record/{requestId}
```

### 查询会话音乐记录

```http
GET /api/music/records/session/{sessionId}
```

### 查询章节音乐记录

```http
GET /api/music/records/session/{sessionId}/segment/{segmentId}
```

### 查询用户音乐记录

```http
GET /api/music/records/user/{userId}?limit=20
```

### 统计处理中任务

```http
GET /api/music/stats/processing
GET /api/music/stats/user/{userId}/processing
```

## 输入参数说明

### Lyria2Input

| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| prompt | String | 是 | 音乐描述提示词，最少1个字符，最多2000个字符 |
| seed | Integer | 否 | 种子值，用于确定性生成 |
| negativePrompt | String | 否 | 负面提示词，描述要排除的内容，默认"low quality" |

### 提示词示例

- `"A peaceful piano melody with gentle rain sounds in the background"`
- `"Upbeat electronic dance music with synthesizer beats"`
- `"Classical orchestral music with strings and woodwinds"`
- `"Ambient soundscape with nature sounds and soft melodies"`
- `"Energetic rock music with electric guitar and drums"`

## 输出结果说明

### Lyria2Output

| 字段 | 类型 | 说明 |
|------|------|------|
| audio | FalFile | 生成的音频文件信息 |

### FalFile

| 字段 | 类型 | 说明 |
|------|------|------|
| url | String | 音频文件下载URL |
| contentType | String | 文件MIME类型（如 audio/wav） |
| fileName | String | 文件名 |
| fileSize | Integer | 文件大小（字节） |

## 数据库记录说明

### AiMusicGenerationRecordPo

音乐生成记录表包含以下主要字段：

| 字段 | 类型 | 说明 |
|------|------|------|
| id | Long | 主键ID |
| sessionId | String | 会话ID |
| segmentId | String | 章节ID |
| userId | String | 用户ID |
| requestId | String | Fal AI请求ID |
| prompt | String | 音乐生成提示词 |
| negativePrompt | String | 负面提示词 |
| seed | Integer | 种子值 |
| status | String | 生成状态 |
| queuePosition | Integer | 队列位置 |
| musicUrl | String | 生成的音乐文件URL |
| musicFileName | String | 音乐文件名 |
| musicFileSize | Long | 音乐文件大小（字节） |
| musicContentType | String | 音乐文件MIME类型 |
| musicDuration | Long | 音乐时长（毫秒） |
| errorMessage | String | 错误信息 |
| consumePoints | Integer | 消耗积分 |
| generationStartTime | Date | 生成开始时间 |
| generationEndTime | Date | 生成完成时间 |
| generationCostTime | Long | 生成耗时（毫秒） |
| requestParams | String | 完整请求参数（JSON格式） |
| responseData | String | 完整响应数据（JSON格式） |

### 状态说明

| 状态 | 说明 |
|------|------|
| PENDING | 等待中 |
| IN_QUEUE | 队列中 |
| IN_PROGRESS | 处理中 |
| COMPLETED | 已完成 |
| FAILED | 失败 |
| CANCELLED | 已取消 |

## 错误处理

客户端内置了完善的错误处理机制：

- **参数验证**：自动验证输入参数的有效性
- **自动重试**：网络错误时自动重试，支持指数退避
- **超时处理**：任务超时自动终止，避免无限等待
- **详细日志**：记录详细的请求和响应日志，便于调试

## 性能优化

- **连接池**：使用 OkHttp 连接池复用连接
- **异步处理**：支持异步生成，提高并发性能
- **轮询优化**：智能轮询间隔，平衡响应速度和资源消耗

## 注意事项

1. **API Key 安全**：请妥善保管 Fal AI API Key，不要在代码中硬编码
2. **生成时间**：音乐生成通常需要 30-120 秒，请耐心等待
3. **文件大小**：生成的音频文件通常为 2-10MB
4. **并发限制**：Fal AI 可能有并发请求限制，请合理控制并发数
5. **费用控制**：每次生成都会消耗 API 配额，请注意费用控制

## 故障排除

### 常见问题

1. **401 Unauthorized**：检查 API Key 是否正确配置
2. **超时错误**：增加超时时间或检查网络连接
3. **参数错误**：检查提示词长度和内容是否符合要求
4. **队列满**：稍后重试或使用异步方式

### 日志级别

- `INFO`：记录重要操作和结果
- `DEBUG`：记录详细的请求响应信息
- `WARN`：记录警告信息和重试
- `ERROR`：记录错误信息和异常

## 示例代码

完整的示例代码请参考 `MusicGenerationExample.java` 文件，包含了各种使用场景的演示。
