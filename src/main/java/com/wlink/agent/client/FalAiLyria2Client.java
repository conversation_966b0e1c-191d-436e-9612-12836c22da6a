package com.wlink.agent.client;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wlink.agent.client.model.music.FalQueueStatus;
import com.wlink.agent.client.model.music.Lyria2Input;
import com.wlink.agent.client.model.music.Lyria2Output;
import com.wlink.agent.config.ExternalApiConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

/**
 * Fal AI Lyria2 音乐生成客户端
 * 基于队列模式的异步音乐生成服务
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-04
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class FalAiLyria2Client {

    private static final MediaType JSON_MEDIA_TYPE = MediaType.get("application/json; charset=utf-8");
    
    // 重试配置
    private static final int MAX_RETRIES = 3;
    private static final long INITIAL_RETRY_DELAY_MS = 1000;
    private static final double RETRY_DELAY_MULTIPLIER = 2.0;
    private static final long MAX_RETRY_DELAY_MS = 10000;
    
    // 轮询配置
    private static final long POLL_INTERVAL_MS = 2000; // 2秒轮询间隔
    private static final long MAX_WAIT_TIME_MS = 300000; // 5分钟超时
    
    private final OkHttpClient httpClient;
    private final ObjectMapper objectMapper;
    private final ExternalApiConfig externalApiConfig;

    /**
     * 提交音乐生成请求到队列
     *
     * @param input 音乐生成输入参数
     * @return 队列状态信息
     * @throws IOException 如果API调用失败
     */
    public FalQueueStatus submitMusicGenerationRequest(Lyria2Input input) throws IOException {
        // 验证输入参数
        input.validate();
        
        String url = externalApiConfig.getFalAiLyria2Url();
        String requestBody = objectMapper.writeValueAsString(input);
        
        log.info("提交音乐生成请求 URL: {}, 请求体: {}", url, requestBody);
        
        Request httpRequest = new Request.Builder()
                .url(url)
                .addHeader("Authorization", "Key " + externalApiConfig.getFalAiLyria2ApiKey())
                .addHeader("Content-Type", "application/json")
                .post(RequestBody.create(requestBody, JSON_MEDIA_TYPE))
                .build();
        
        try (Response response = httpClient.newCall(httpRequest).execute()) {
            String responseBody = response.body() != null ? response.body().string() : null;
            
            log.info("收到提交响应 状态码: {}, 响应体: {}", response.code(), responseBody);
            
            if (!response.isSuccessful()) {
                log.error("提交音乐生成请求失败: Code={}, Message={}, Body={}", 
                         response.code(), response.message(), responseBody);
                throw new IOException("API请求失败: " + response.code() + " " + responseBody);
            }
            
            if (responseBody == null || responseBody.isEmpty()) {
                throw new IOException("收到空响应体");
            }
            
            return objectMapper.readValue(responseBody, FalQueueStatus.class);
        }
    }

    /**
     * 查询任务状态
     * 
     * @param requestId 请求ID
     * @return 队列状态信息
     * @throws IOException 如果API调用失败
     */
    public FalQueueStatus getTaskStatus(String requestId) throws IOException {
        String url = externalApiConfig.getFalAiStatusUrl(requestId);
        
        log.debug("查询任务状态 URL: {}, 请求ID: {}", url, requestId);
        
        Request httpRequest = new Request.Builder()
                .url(url)
                .addHeader("Authorization", "Key " + externalApiConfig.getFalAiLyria2ApiKey())
                .get()
                .build();
        
        try (Response response = httpClient.newCall(httpRequest).execute()) {
            String responseBody = response.body() != null ? response.body().string() : null;
            
            log.debug("收到状态查询响应 状态码: {}, 响应体: {}", response.code(), responseBody);
            
            if (!response.isSuccessful()) {
                log.error("查询任务状态失败: Code={}, Message={}, Body={}", 
                         response.code(), response.message(), responseBody);
                throw new IOException("状态查询失败: " + response.code() + " " + responseBody);
            }
            
            if (responseBody == null || responseBody.isEmpty()) {
                throw new IOException("收到空响应体");
            }
            
            return objectMapper.readValue(responseBody, FalQueueStatus.class);
        }
    }

    /**
     * 获取任务结果
     * 
     * @param requestId 请求ID
     * @return 音乐生成结果
     * @throws IOException 如果API调用失败
     */
    public Lyria2Output getTaskResult(String requestId) throws IOException {
        String url = externalApiConfig.getFalAiResultUrl(requestId);
        
        log.info("获取任务结果 URL: {}, 请求ID: {}", url, requestId);
        
        Request httpRequest = new Request.Builder()
                .url(url)
                .addHeader("Authorization", "Key " + externalApiConfig.getFalAiLyria2ApiKey())
                .get()
                .build();
        
        try (Response response = httpClient.newCall(httpRequest).execute()) {
            String responseBody = response.body() != null ? response.body().string() : null;
            
            log.info("收到结果响应 状态码: {}, 响应体: {}", response.code(), responseBody);
            
            if (!response.isSuccessful()) {
                log.error("获取任务结果失败: Code={}, Message={}, Body={}", 
                         response.code(), response.message(), responseBody);
                throw new IOException("获取结果失败: " + response.code() + " " + responseBody);
            }
            
            if (responseBody == null || responseBody.isEmpty()) {
                throw new IOException("收到空响应体");
            }
            
            return objectMapper.readValue(responseBody, Lyria2Output.class);
        }
    }

    /**
     * 取消任务
     * 
     * @param requestId 请求ID
     * @return 是否成功取消
     * @throws IOException 如果API调用失败
     */
    public boolean cancelTask(String requestId) throws IOException {
        String url = externalApiConfig.getFalAiBaseUrl() + "/fal-ai/lyria2/requests/" + requestId + "/cancel";
        
        log.info("取消任务 URL: {}, 请求ID: {}", url, requestId);
        
        Request httpRequest = new Request.Builder()
                .url(url)
                .addHeader("Authorization", "Key " + externalApiConfig.getFalAiLyria2ApiKey())
                .put(RequestBody.create("", JSON_MEDIA_TYPE))
                .build();
        
        try (Response response = httpClient.newCall(httpRequest).execute()) {
            String responseBody = response.body() != null ? response.body().string() : null;
            
            log.info("收到取消响应 状态码: {}, 响应体: {}", response.code(), responseBody);
            
            if (!response.isSuccessful()) {
                log.error("取消任务失败: Code={}, Message={}, Body={}", 
                         response.code(), response.message(), responseBody);
                return false;
            }
            
            return true;
        }
    }

    /**
     * 同步生成音乐（等待完成）
     * 提交请求后自动轮询直到完成或超时
     *
     * @param input 音乐生成输入参数
     * @return 音乐生成结果
     * @throws IOException 如果API调用失败或超时
     */
    public Lyria2Output generateMusicSync(Lyria2Input input) throws IOException {
        log.info("开始同步音乐生成: {}", input.getPrompt());

        // 提交请求
        FalQueueStatus queueStatus = submitMusicGenerationRequestWithRetry(input);
        String requestId = queueStatus.getRequestId();

        log.info("音乐生成请求已提交: requestId={}, status={}", requestId, queueStatus.getStatus());

        // 轮询等待完成
        return waitForCompletion(requestId);
    }

    /**
     * 带重试的提交请求
     *
     * @param input 音乐生成输入参数
     * @return 队列状态信息
     * @throws IOException 如果所有重试都失败
     */
    public FalQueueStatus submitMusicGenerationRequestWithRetry(Lyria2Input input) throws IOException {
        FalQueueStatus queueStatus = null;

        for (int attempt = 1; attempt <= MAX_RETRIES; attempt++) {
            try {
                queueStatus = submitMusicGenerationRequest(input);
                log.info("提交音乐生成请求成功: requestId={}, status={}",
                        queueStatus.getRequestId(), queueStatus.getStatus());
                break;
            } catch (IOException e) {
                if (attempt == MAX_RETRIES) {
                    log.error("提交音乐生成请求失败，重试次数用尽: {}", e.getMessage());
                    throw e;
                }

                long retryDelay = calculateRetryDelayMs(attempt);
                log.warn("提交音乐生成请求失败，将在 {}ms 后重试（{}/{}）: {}",
                        retryDelay, attempt, MAX_RETRIES, e.getMessage());

                try {
                    Thread.sleep(retryDelay);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new IOException("等待重试时被中断", ie);
                }
            }
        }

        if (queueStatus == null) {
            throw new IOException("提交音乐生成请求失败，未获取到队列状态");
        }

        return queueStatus;
    }

    /**
     * 等待任务完成
     *
     * @param requestId 请求ID
     * @return 音乐生成结果
     * @throws IOException 如果轮询失败或超时
     */
    public Lyria2Output waitForCompletion(String requestId) throws IOException {
        log.info("开始轮询任务状态: requestId={}", requestId);

        long startTime = System.currentTimeMillis();

        while (System.currentTimeMillis() - startTime < MAX_WAIT_TIME_MS) {
            try {
                FalQueueStatus status = getTaskStatus(requestId);
                log.debug("任务状态: requestId={}, status={}, description={}",
                         requestId, status.getStatus(), status.getStatusDescription());

                if (status.isCompleted()) {
                    log.info("任务已完成，获取结果: requestId={}", requestId);
                    return getTaskResultWithRetry(requestId);
                }

                if (status.isInQueue() && status.getQueuePosition() != null) {
                    log.info("任务在队列中: requestId={}, 位置={}, 预估等待时间={}",
                            requestId, status.getQueuePosition(), status.getEstimatedWaitTime());
                } else if (status.isInProgress()) {
                    log.info("任务处理中: requestId={}", requestId);
                }

                // 等待下次轮询
                Thread.sleep(POLL_INTERVAL_MS);

            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new IOException("轮询被中断", e);
            } catch (IOException e) {
                log.warn("轮询状态失败，继续重试: {}", e.getMessage());
                try {
                    Thread.sleep(POLL_INTERVAL_MS);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new IOException("轮询被中断", ie);
                }
            }
        }

        throw new IOException("任务超时: requestId=" + requestId + ", 超时时间=" + (MAX_WAIT_TIME_MS / 1000) + "秒");
    }

    /**
     * 带重试的获取结果
     *
     * @param requestId 请求ID
     * @return 音乐生成结果
     * @throws IOException 如果所有重试都失败
     */
    public Lyria2Output getTaskResultWithRetry(String requestId) throws IOException {
        for (int attempt = 1; attempt <= MAX_RETRIES; attempt++) {
            try {
                return getTaskResult(requestId);
            } catch (IOException e) {
                if (attempt == MAX_RETRIES) {
                    log.error("获取任务结果失败，重试次数用尽: {}", e.getMessage());
                    throw e;
                }

                long retryDelay = calculateRetryDelayMs(attempt);
                log.warn("获取任务结果失败，将在 {}ms 后重试（{}/{}）: {}",
                        retryDelay, attempt, MAX_RETRIES, e.getMessage());

                try {
                    Thread.sleep(retryDelay);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new IOException("等待重试时被中断", ie);
                }
            }
        }

        throw new IOException("获取任务结果失败");
    }

    /**
     * 计算重试延迟时间
     *
     * @param attempt 重试次数
     * @return 延迟时间（毫秒）
     */
    private long calculateRetryDelayMs(int attempt) {
        long delay = (long) (INITIAL_RETRY_DELAY_MS * Math.pow(RETRY_DELAY_MULTIPLIER, attempt - 1));
        return Math.min(delay, MAX_RETRY_DELAY_MS);
    }
}
