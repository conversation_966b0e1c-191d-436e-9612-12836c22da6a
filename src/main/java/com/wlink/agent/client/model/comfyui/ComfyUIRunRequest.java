package com.wlink.agent.client.model.comfyui;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * ComfyUI 运行请求
 */
@Data
public class ComfyUIRunRequest {
    
    /**
     * Web应用ID
     */
    @JsonProperty("webappId")
    private Long webappId;
    
    /**
     * API密钥
     */
    @JsonProperty("apiKey")
    private String apiKey;

    @JsonProperty("instanceType")
    private String instanceType;
    
    /**
     * 节点信息列表
     */
    @JsonProperty("nodeInfoList")
    private List<ComfyUINodeInfo> nodeInfoList;


    @JsonProperty("webhookUrl")
    private String webhookUrl;
}
