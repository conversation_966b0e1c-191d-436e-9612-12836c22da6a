package com.wlink.agent.client.model.dify;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Dify完成消息API输入参数模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DifyCompletionInputs {
    
    /**
     * 查询内容
     */
    @JsonProperty("query")
    private String query;
}
