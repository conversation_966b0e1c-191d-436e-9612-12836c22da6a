package com.wlink.agent.client.model.dify;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Dify完成消息API请求模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DifyCompletionRequest {
    
    /**
     * 输入参数
     */
    @JsonProperty("inputs")
    private DifyCompletionInputs inputs;
    
    /**
     * 响应模式，默认为blocking
     */
    @JsonProperty("response_mode")
    @Builder.Default
    private String responseMode = "blocking";
    
    /**
     * 用户标识
     */
    @JsonProperty("user")
    private String user;

    /**
     * 文件列表
     */
    @JsonProperty("files")
    private List<DifyFileInfo> files;
}
