package com.wlink.agent.client.model.dify;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Dify完成消息API响应模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DifyCompletionResponse {
    
    /**
     * 事件类型
     */
    @JsonProperty("event")
    private String event;
    
    /**
     * 任务ID
     */
    @JsonProperty("task_id")
    private String taskId;
    
    /**
     * 消息ID
     */
    @JsonProperty("id")
    private String id;
    
    /**
     * 消息ID（与id字段相同）
     */
    @JsonProperty("message_id")
    private String messageId;
    
    /**
     * 模式
     */
    @JsonProperty("mode")
    private String mode;
    
    /**
     * 回答内容
     */
    @JsonProperty("answer")
    private String answer;
    
    /**
     * 创建时间
     */
    @JsonProperty("created_at")
    private Long createdAt;
}
