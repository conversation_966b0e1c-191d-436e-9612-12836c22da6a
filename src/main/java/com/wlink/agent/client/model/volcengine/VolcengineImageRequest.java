package com.wlink.agent.client.model.volcengine;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.checkerframework.checker.units.qual.N;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.List;

@Data
@Schema(description = "火山引擎文生图请求")
public class VolcengineImageRequest {


    //会话id
    @NotBlank(message = "conversationId is required")
    @Schema(description = "会话id")
    private String conversationId;

    //类型  3-角色  4-分镜  5-参考图
    @NotNull(message = "type is required")
    @Schema(description = "类型  3-角色  4-分镜  5-参考图")
    private Integer type;

    @NotBlank(message = "contentId is required")
    @Schema(description = "内容id")
    private String contentId;

    @Schema(description = "请求类型Key", example = "high_aes_general_v21_L")
    @JsonProperty("req_key")
    private String reqKey = "high_aes_general_v21_L";

    @NotBlank(message = "prompt is required")
    @Schema(description = "图像描述文本", required = true)
    @JsonProperty("prompt")
    private String prompt;

    /**
     * 中文prompt
     */
    @Schema(description = "中文prompt")
    @JsonProperty("cnPrompt")
    private String cnPrompt;


    @Schema(description = "随机种子, -1表示随机", example = "-1")
    @JsonProperty("seed")
    private Integer seed;

    @Schema(description = "相关性，控制图像与输入文本的匹配程度", example = "3.5")
    @JsonProperty("scale")
    private Float scale = 3.5f;

    @Schema(description = "DDIM采样步数", example = "16")
    @JsonProperty("ddim_steps")
    private Integer ddimSteps = 16;

    @Schema(description = "图像宽度", example = "512")
    @JsonProperty("width")
    private Integer width;

    @Schema(description = "图像高度", example = "512")
    @JsonProperty("height")
    private Integer height;

    @Schema(description = "是否使用超分辨率", example = "true")
    @JsonProperty("use_sr")
    private Boolean useSr = true;

    @Schema(description = "是否使用预处理LLM", example = "true")
    @JsonProperty("use_pre_llm")
    private Boolean usePreLlm = true;

    @Schema(description = "是否返回图片URL", example = "true")
    @JsonProperty("return_url")
    private Boolean returnUrl = true;

    @Schema(description = "Logo信息")
    @JsonProperty("logo_info")
    private LogoInfo logoInfo;

    @Schema(description = "供应DOUBAO-豆包   FLUX-flux  ", example = "DOUBAO")
    @JsonProperty("imageModel")
    private String imageModel = "DOUBAO";

    @Schema(description = "输入图片URL列表，包含人, required = true, example = ", required = true, example = "[")
    @JsonProperty("imageUrls")
    private List<String> imageUrls;




}
