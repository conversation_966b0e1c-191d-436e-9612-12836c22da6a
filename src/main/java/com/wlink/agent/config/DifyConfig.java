package com.wlink.agent.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * Dify API配置类
 */
@Data
@RefreshScope
@Configuration
@ConfigurationProperties(prefix = "dify")
public class DifyConfig {



//    @Value("${dify.image.prefix:http://61.153.100.125:30080}")
//    private String difyImagePrefix;
//
//    @Value("${dify.prompt.api.key:app-CnhY7gaQSWKdRXV1JZBKDy2y}")
//    private String difyPromptApiKey;
//
//    @Value("${dify.image.api.key:app-E2rAPLz8Wc1anS5Q0udJBczl}")
//    private String difyImageApiKey;
//
//    @Value("${dify.style.api.key:app-OoEI1NOylpZZR8KgIcNrkdT7}")
//    private String difyStyleApiKey;

//    @Value("${dify.background.tidy.key:app-EktDddAsmk4hwrByRtwbmbAd}")
//    private String backgroundTidyKey;

//    @Value("${live.title.apiKey:app-8jAq3zX8jxGSmsQ6JEhjcw5z}")
//    private String titleApiKey;


//    @Value("${dify.run.down.key:app-ZEvdQ0vMLj3jasFEutFyhtVE}")
//    private String difyRunDownKey;

//
//@Value("${dify.danmaku.apiKey:app-U0DmiaWsr5XQzz7oeYNeidZy}")
//private String difyApiKey;


    private String imagePrefix = "http://61.153.100.125:30080";
    /**
     * 提示词生成API Key
     */
    private String promptApiKey = "app-CnhY7gaQSWKdRXV1JZBKDy2y";

    /**
     * 图片生成API Key
     */
    private String imageApiKey = "app-E2rAPLz8Wc1anS5Q0udJBczl";

    /**
     * 风格生成API Key
     */
    private String styleApiKey = "app-OoEI1NOylpZZR8KgIcNrkdT7";

    /**
     * 直播内容生成API Key
     */
    private String runDownKey = "app-ZEvdQ0vMLj3jasFEutFyhtVE";
    /**
     * 直播标题生成API Key
     */
    private String liveTitleKey = "app-8jAq3zX8jxGSmsQ6JEhjcw5z";


    private String backgroundTidyKey = "app-EktDddAsmk4hwrByRtwbmbAd";

    /**
     * 直播弹幕回复API Key
     */
    private String danmakuReplyApiKey = "app-U0DmiaWsr5XQzz7oeYNeidZy";

    /**
     * API Base URL
     */
    private String baseUrl = "https://api.dify.ai/v1";




} 