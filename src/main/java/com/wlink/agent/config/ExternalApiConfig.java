package com.wlink.agent.config;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * Dify API配置类
 * 集中管理所有Dify API的URL和密钥配置
 */
@Configuration
@RefreshScope
public class ExternalApiConfig {

    /**
     * Dify API基础URL
     * 默认值为 http://dify.weilitech.cn
     * -- GETTER --
     *  获取基础URL
     *
     * @return 基础URL

     */
    @Getter
    @Value("${dify.base-url:http://dify.weilitech.cn}")
    private String baseUrl;

    /**
     * Dify 完成API的路径
     * 默认值为 /v1/completion-messages
     */
    @Value("${dify.paths.completion:/v1/completion-messages}")
    private String completionPath;

    /**
     * Dify 图像修改API的路径
     * 默认值为 /v1/workflows/run
     */
    @Value("${dify.paths.image-modify:/v1/workflows/run}")
    private String imageModifyPath;

    /**
     * Dify 文本API的路径
     * 默认值为 /v1/workflows/run
     */
    @Value("${dify.paths.text:/v1/workflows/run}")
    private String textPath;

    /**
     * Dify 聊天API的路径
     * 默认值为 /v1
     */
    @Value("${dify.paths.chat:/v1}")
    private String chatPath;

    /**
     * Dify 完成API密钥
     * 默认值为 app-zVZL2hTcvYtps6W6PKifpefi
     * -- GETTER --
     *  获取完成API密钥
     *
     * @return 完成API密钥

     */
    @Getter
    @Value("${dify.api-keys.completion:app-zVZL2hTcvYtps6W6PKifpefi}")
    private String completionApiKey;

    /**
     * Dify 图像修改API密钥
     * 默认值为 app-bJTOugSyHPqNNCCQI1qinEvd
     * -- GETTER --
     *  获取图像修改API密钥
     *
     * @return 图像修改API密钥

     */
    @Getter
    @Value("${dify.api-keys.image-modify:app-bJTOugSyHPqNNCCQI1qinEvd}")
    private String imageModifyApiKey;

    /**
     * Dify 文本API密钥
     * 默认值为 app-uVC0bfEURstqtuK9pUejsy8N
     * -- GETTER --
     *  获取文本API密钥
     *
     * @return 文本API密钥

     */
    @Getter
    @Value("${dify.api-keys.text:app-uVC0bfEURstqtuK9pUejsy8N}")
    private String textApiKey;

    /**
     * Dify 聊天API密钥
     * 默认值为 app-yVll3rk5jqUQjeFtbDq4pyOz
     * -- GETTER --
     *  获取聊天API密钥
     *
     * @return 聊天API密钥

     */
    @Getter
    @Value("${dify.api-keys.chat:app-4PwzhLmMHSvqzYinqunnlatR}")
    private String chatApiKey;

    /**
     * diyf 文本翻译API密钥
     * -- GETTER --
     *  获取文本翻译API密钥
     *
     * @return 文本翻译API密钥

     */
    @Getter
    @Value("${dify.api-keys.text-translate:app-ln3SlTsUDcm85ztYVtZNuie1}")
    private String textTranslateApiKey;


    /**
     * 提示词优化key
     */
    @Getter
    @Value("${dify.api-keys.prompt-optimize:app-3mTTfJINfFgvBMEyGSlGiZtu}")
    private String textOptimizeApiKey;


    /**
     * Fal AI Lyria2 音乐生成API密钥
     * -- GETTER --
     *  获取Fal AI Lyria2 API密钥
     *
     * @return Fal AI Lyria2 API密钥
     */
    @Getter
    @Value("${fal-ai.api-keys.lyria2:}")
    private String falAiLyria2ApiKey;

    /**
     * Fal AI 基础URL
     * 默认值为 https://queue.fal.run
     * -- GETTER --
     *  获取Fal AI基础URL
     *
     * @return Fal AI基础URL
     */
    @Getter
    @Value("${fal-ai.base-url:https://queue.fal.run}")
    private String falAiBaseUrl;




    /**
     * 获取完成API的完整URL
     *
     * @return 完成API的完整URL
     */
    public String getCompletionUrl() {
        return baseUrl + completionPath;
    }

    /**
     * 获取图像修改API的完整URL
     *
     * @return 图像修改API的完整URL
     */
    public String getImageModifyUrl() {
        return baseUrl + imageModifyPath;
    }

    /**
     * 获取文本API的完整URL
     *
     * @return 文本API的完整URL
     */
    public String getTextUrl() {
        return baseUrl + textPath;
    }

    /**
     * 获取Fal AI Lyria2音乐生成API的完整URL
     *
     * @return Fal AI Lyria2 API的完整URL
     */
    public String getFalAiLyria2Url() {
        return falAiBaseUrl + "/fal-ai/lyria2";
    }

    /**
     * 获取Fal AI请求状态查询URL
     *
     * @param requestId 请求ID
     * @return 状态查询URL
     */
    public String getFalAiStatusUrl(String requestId) {
        return falAiBaseUrl + "/fal-ai/lyria2/requests/" + requestId + "/status";
    }

    /**
     * 获取Fal AI请求结果URL
     *
     * @param requestId 请求ID
     * @return 结果获取URL
     */
    public String getFalAiResultUrl(String requestId) {
        return falAiBaseUrl + "/fal-ai/lyria2/requests/" + requestId;
    }

    /**
     * 获取聊天API的完整URL
     *
     * @return 聊天API的完整URL
     */
    public String getChatUrl() {
        return baseUrl + chatPath;
    }


}