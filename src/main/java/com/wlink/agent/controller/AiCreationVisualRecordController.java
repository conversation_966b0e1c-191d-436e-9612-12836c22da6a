package com.wlink.agent.controller;

import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.wlink.agent.annotation.IgnoreRequestUser;
import com.wlink.agent.model.req.RenderTriggerReq;
import com.wlink.agent.model.req.UpdateVisualRecordStatusReq;
import com.wlink.agent.model.req.VisualRecordPageReq;
import com.wlink.agent.model.res.AiCreationVisualRecordListRes;
import com.wlink.agent.model.res.AiCreationVisualRecordRes;
import com.wlink.agent.model.res.SessionReferenceImageRes;
import com.wlink.agent.model.res.VisualRecordShareRes;
import com.wlink.agent.service.AiCreationVisualRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * AI创作视觉记录控制
 */
@Slf4j
@RestController
@RequestMapping("/agent/ai-creation/visual-record")
@Tag(name = "AI创作视觉记录")
@Validated
@RequiredArgsConstructor
public class AiCreationVisualRecordController {

    private final AiCreationVisualRecordService visualRecordService;
    // Removed VisualRenderService and ObjectMapper injections

    //平台地址
    @Value("${platform.url:https://dev.neodomain.cn}")
    private String platformUrl;

    /**
     * 获取会话的所有视觉记
     * @param conversationId 会话ID
     * @return 视觉记录列表
     */
    @GetMapping("/list/{conversationId}")
    @Operation(summary = "获取会话视觉记录列表", description = "根据会话ID获取所有的视觉创作记录")
    public MultiResponse<AiCreationVisualRecordRes> listVisualRecords(
            @Parameter(description = "会话ID", required = true) @PathVariable String conversationId) {
        log.info("Request to list visual records for conversationId: {}", conversationId);
        List<AiCreationVisualRecordRes> records = visualRecordService.getVisualRecordsBySession(conversationId);
        return MultiResponse.of(records);
    }

    /**
     * 获取同会话下所有已发布的记
     * @param token 视觉记录Code
     * @return 同会话下已发布的视觉记录列表
     */
    @GetMapping("/list/published/{token}")
@Operation(summary = "鏌ヨPublished Records By Code鍒楄〃")
    public MultiResponse<AiCreationVisualRecordListRes> listPublishedRecordsByCode(
            @Parameter(description = "视觉记录Code", required = true) @PathVariable String token) {
        log.info("Request to list published visual records by code: {}", token);
        List<AiCreationVisualRecordListRes> records = visualRecordService.getPublishedVisualRecordsByCode(token);
        return MultiResponse.of(records);
    }



    /**
     * 分页查询已发布的视觉记录
     * @param req 分页查询参数
     * @return 分页结果
     */
    @GetMapping("/list/published/home")
@Operation(summary = "分页查询已发布的视觉记录")
    public PageResponse<AiCreationVisualRecordListRes> listPublishedRecordsByPage(
            @Parameter(description = "分页查询参数", required = true) @Valid VisualRecordPageReq req) {
        log.info("Request to list published visual records with pagination: {}", req);
        return visualRecordService.getPublishedRecordsByPage(req.getPageNum(), req.getPageSize());
    }

    /**
     * 更新视觉记录的发布状(发布/取消发布)
     * @param req 请求体，包含visualRecordId和publish状
     * @return 发布时返回分享链接信息，取消发布时返回成功响
     */
    @PostMapping("/status/update")
@Operation(summary = "鏇存柊Publish Status")
    public Response updatePublishStatus(
            @Parameter(description = "更新状态请求体", required = true) @Valid @RequestBody UpdateVisualRecordStatusReq req) {
        log.info("Request to update publish status: {}", req);
        String shareToken = visualRecordService.updatePublishStatus(req);
        if (Boolean.TRUE.equals(req.getPublish())) {
            if (shareToken == null) {
                 log.error("Publish operation succeeded but shareToken is null for req: {}", req);
                 return Response.buildFailure("PUBLISH_FAILED", "发布操作成功，但获取分享链接失败");
            }
            String shareUrl = platformUrl + "/share?token=" + shareToken;
            
            VisualRecordShareRes shareRes = new VisualRecordShareRes();
            shareRes.setShareToken(shareToken);
            shareRes.setShareUrl(shareUrl);
            log.info("Successfully published visual record: {}, shareToken: {}", req.getVisualRecordId(), shareToken);
            return SingleResponse.of(shareRes);
        } else {
            log.info("Successfully unpublished visual record: {}", req.getVisualRecordId());
            return Response.buildSuccess();
        }
    }

    /**
     * 根据分享令牌获取视觉记录 (公开访问，无需登录)
     * @param shareToken 分享令牌
     * @return 视觉记录
     */
    @IgnoreRequestUser
    @GetMapping("/share/{shareToken}")
@Operation(summary = "鑾峰彇Visual Record By Share Token")
    public SingleResponse<AiCreationVisualRecordRes> getVisualRecordByShareToken(
            @Parameter(description = "分享令牌", required = true) @PathVariable String shareToken) {
        log.info("Request to get shared visual record by token: {}", shareToken);
        return SingleResponse.of(visualRecordService.getVisualRecordByShareToken(shareToken));
    }

    /**
     * 根据ID获取视觉记录 (需要登录验
     * @param visualRecordId 视觉记录ID
     * @return 视觉记录
     */
    @GetMapping("/{visualRecordId}")
@Operation(summary = "鑾峰彇Visual Record By Id")
    public SingleResponse<AiCreationVisualRecordRes> getVisualRecordById(
            @Parameter(description = "视觉记录ID", required = true) @PathVariable Long visualRecordId) {
        log.info("Request to get visual record by id: {}", visualRecordId);
        AiCreationVisualRecordRes record = visualRecordService.getVisualRecordByIdWithCheck(visualRecordId);
        return SingleResponse.of(record);
    }

    /**
     * 触发指定会话记录的视频渲
     * @param req 包含 sessionId 的请求体
     * @return 接受渲染请求的响
     */
    @PostMapping("/render")
@Operation(summary = "Trigger Render")
    public Response triggerRender(
            @Parameter(description = "渲染触发请求", required = true) @Valid @RequestBody RenderTriggerReq req) {
        log.info("Controller: Request received to trigger render for  visualRecordCode: {}", req.getVisualRecordCode());
        visualRecordService.triggerRender(req);
        log.info("Controller: Successfully submitted render request for  visualRecordCode: {}", req.getVisualRecordCode());
        return Response.buildSuccess();
    }

    /**
     * 删除指定的视觉记(逻辑删除)
     * @param visualRecordCode 要删除的视觉记录ID
     * @return 成功响应
     */
    @DeleteMapping("/{visualRecordCode}")
@Operation(summary = "鍒犻櫎Visual Record")
    public Response deleteVisualRecord(
            @Parameter(description = "视觉记录ID", required = true) @PathVariable String visualRecordCode) {
        log.info("Controller: Request received to delete visual record ID: {}", visualRecordCode);
        visualRecordService.deleteVisualRecordByCode(visualRecordCode);
        log.info("Controller: Successfully processed delete request for visual record ID: {}", visualRecordCode);
        return Response.buildSuccess();
    }
} 
