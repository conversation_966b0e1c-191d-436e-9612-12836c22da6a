package com.wlink.agent.controller;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.cola.exception.BizException;
import com.wlink.agent.annotation.IgnoreRequestUser;
import com.wlink.agent.exception.ErrorCodeEnum;
import com.wlink.agent.model.dto.SimpleUserInfo;
import com.wlink.agent.model.req.AiCreationSessionQueryReq;
import com.wlink.agent.model.req.ChatMessageRequest;
import com.wlink.agent.model.req.CreateConversationRequest;
import com.wlink.agent.model.req.PromptOptimizeReq;
import com.wlink.agent.model.req.UpdateSessionReq;
import com.wlink.agent.model.res.ConversationListResponse;
import com.wlink.agent.model.res.ConversationMessageListResponse;
import com.wlink.agent.model.res.PromptOptimizeRes;
import com.wlink.agent.model.vo.AiCreationSessionInfoVo;
import com.wlink.agent.model.vo.AiCreationSessionVO;
import com.wlink.agent.model.vo.ConversationItem;
import com.wlink.agent.service.ApiKeyValidationService;
import com.wlink.agent.service.ChatService;
import com.wlink.agent.utils.UserContext;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;


@Tag(name = "聊天与会话管理接口")
@RestController
@RequestMapping("/agent/chat")
public class ChatController {

    private static final Logger log = LoggerFactory.getLogger(ChatController.class);

    private final ChatService chatService;
    private final ApiKeyValidationService apiKeyValidationService;

    @Autowired
    public ChatController(ChatService chatService, ApiKeyValidationService apiKeyValidationService) {
        this.chatService = chatService;
        this.apiKeyValidationService = apiKeyValidationService;
    }

    /**
     * 处理聊天消息请求并建立SSE连接
     * @param request 请求体，包含聊天所需信息
     * @return SseEmitter 用于服务器发送事件
     */
    @Operation(summary = "发送聊天消息(SSE)", description = "发送聊天消息并建立SSE连接以接收流式响应")
    @PostMapping(value = "/messages", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter streamChatMessages(@Parameter(description = "聊天消息请求体", required = true) @RequestBody ChatMessageRequest request) {
        log.info("Received request for SSE stream with data: {}", request);
        try {
            // 调用Service处理请求并获取SseEmitter
            return chatService.handleChatMessage(request);
        } catch (Exception e) {
            log.error("Error creating SSE emitter: {}", e.getMessage(), e);
            // 如果Service层抛出异常，创建一个立即完成并带有错误的Emitter
            SseEmitter errorEmitter = new SseEmitter();
            errorEmitter.completeWithError(e);
            return errorEmitter;
        }
    }

    /**
     * 获取会话列表
     * @param limit 返回数量限制（默认20）
     * @return ConversationListResponse 会话列表
     */
    @Operation(summary = "获取Dify会话列表")
    @GetMapping("/conversations")
    public SingleResponse<ConversationListResponse> getConversations(
            @Parameter(description = "返回数量限制", example = "20") @RequestParam(value = "limit", defaultValue = "20") int limit) {
        return SingleResponse.of(chatService.getConversationList(limit));
    }
    
    /**
     * 获取会话历史记录
     * @param conversationId 会话ID（必填）
     * @param limit 返回数量限制（默认20）
     * @return ConversationMessageListResponse 会话消息列表
     */
    @Operation(summary = "获取Dify会话历史记录")
    @GetMapping("/user/messages")
    public SingleResponse<ConversationMessageListResponse> getConversationMessages(
            @Parameter(description = "Dify会话ID", required = true) @RequestParam("conversation_id") String conversationId,
            @Parameter(description = "返回数量限制", example = "20") @RequestParam(value = "limit", defaultValue = "20") int limit) {
        return SingleResponse.of(chatService.getConversationMessages(conversationId, limit));
    }
    
    /**
     * 根据会话ID删除会话
     * @param conversationId 会话ID
     */
    @Operation(summary = "根据Dify会话ID删除会话")
    @DeleteMapping("/conversation/{conversationId}")
    public Response deleteConversation(@Parameter(description = "Dify会话ID", required = true) @PathVariable("conversationId") String conversationId) {
        log.info("Received request to delete conversation: conversationId={}", conversationId);
        chatService.deleteConversation(conversationId);
        return SingleResponse.buildSuccess();
    }
    
    /**
     * 创建会话
     * @param request 创建会话请求体
     * @return ConversationItem 新建会话
     */
    @Operation(summary = "创建AI创作会话")
    @PostMapping("/conversation")
    public SingleResponse<ConversationItem> createConversation(@Parameter(description = "创建会话请求体", required = true) @RequestBody CreateConversationRequest request) {
        return SingleResponse.of(chatService.createConversation(request));
    }

    /**
     * 通过API Key创建会话（无需用户登录）
     *
     * @param apiKey  API Key，从请求头获取
     * @param request 创建会话请求体
     * @return ConversationItem 新建会话
     */
    @IgnoreRequestUser
    @Operation(summary = "通过API Key创建AI创作会话", description = "使用API Key验证身份并创建会话，无需用户登录")
    @PostMapping("/api/conversation")
    public SingleResponse<ConversationItem> createConversationWithApiKey(
            @Parameter(description = "API Key", required = true) @RequestHeader(value = "api-key") String apiKey,
            @Parameter(description = "创建会话请求体", required = true) @RequestBody CreateConversationRequest request) {
        log.info("Received API request to create conversation with API Key");
        // 验证API Key并获取用户ID
        String userId = apiKeyValidationService.validateApiKeyAndGetUserId(apiKey);
        if (!StringUtils.hasText(userId)) {
            throw new BizException(ErrorCodeEnum.API_KEY_INVALID.getCode(), "Invalid API Key");
        }
        SimpleUserInfo simpleUserInfo = new SimpleUserInfo();
        simpleUserInfo.setUserId(userId);
        UserContext.setUser(simpleUserInfo);
        // 调用原有的创建会话逻辑
        return SingleResponse.of(chatService.createConversation(request));
    }

    
    /**
     * 获取当前用户的AI创作会话列表（分页查询）
     * @param queryReq 分页查询请求（包含页码、每页大小、状态查询条件）
     * @return PageResponse<AiCreationSessionVO> 分页的AI创作会话列表
     */
    @Operation(summary = "获取当前用户的AI创作会话列表（分页查询）")
    @GetMapping("/sessions")
    public PageResponse<AiCreationSessionVO> getAiCreationSessionsForCurrentUser(AiCreationSessionQueryReq queryReq) {
        log.info("Received request for current user's AI creation session list with params: {}", queryReq);
        return chatService.getAiCreationSessionsByUserId(queryReq);
    }
    
    /**
     * 停止响应内容
     * @param conversationId 会话ID
     * @return Response 操作结果
     */
    @Operation(summary = "停止响应内容", description = "根据会话ID停止当前正在进行的响应")
    @PostMapping("/stop-response/{conversationId}")
    public Response stopResponse(@Parameter(description = "会话ID", required = true) @PathVariable("conversationId") String conversationId) {
        log.info("Received request to stop response: conversationId={}", conversationId);
        boolean b = chatService.stopResponse(conversationId);
        if (!b) {
            return SingleResponse.buildFailure(ErrorCodeEnum.USER_STOP_RESPONSE_FAILED.getCode(),ErrorCodeEnum.USER_STOP_RESPONSE_FAILED.getMsg());
        }
        return SingleResponse.buildSuccess();
    }
    
    /**
     * 清空上下文
     * @param conversationId 会话ID
     * @return Response 操作结果
     */
    @Operation(summary = "清空上下文", description = "清空指定会话的上下文信息")
    @PostMapping("/clear-context/{conversationId}")
    public Response clearContext(@Parameter(description = "会话ID", required = true) @PathVariable("conversationId") String conversationId) {
        log.info("Received request to clear context: conversationId={}", conversationId);
        boolean context = chatService.clearContext(conversationId);
        if (!context) {
            return SingleResponse.buildFailure(ErrorCodeEnum.USER_CLEAR_CONTEXT_FAILED.getCode(),ErrorCodeEnum.USER_CLEAR_CONTEXT_FAILED.getMsg());
        }
        return SingleResponse.buildSuccess();
    }
    
    /**
     * 根据会话ID查询会话信息
     * @param conversationId 会话ID
     * @return SingleResponse<AiCreationSessionInfoVo> 会话信息
     */
    @Operation(summary = "根据会话ID查询会话信息", description = "获取指定会话ID的详细信息")
    @GetMapping("/session/{conversationId}")
    public SingleResponse<AiCreationSessionInfoVo> getSessionById(@Parameter(description = "会话ID", required = true) @PathVariable("conversationId") String conversationId) {
        log.info("Received request to get session info: sessionId={}", conversationId);
        return chatService.getSessionInfoById(conversationId);
    }
    
    /**
     * 公开接口：根据会话ID查询会话信息（无需用户登录）
     * @param conversationId 会话ID
     * @return SingleResponse<AiCreationSessionInfoVo> 会话信息
     */
    @IgnoreRequestUser
    @Operation(summary = "公开接口：根据会话ID查询会话信息", description = "获取指定会话ID的详细信息，无需用户登录")
    @GetMapping("/public/session/{conversationId}")
    public SingleResponse<AiCreationSessionInfoVo> getPublicSessionById(@Parameter(description = "会话ID", required = true) @PathVariable("conversationId") String conversationId) {
        log.info("Received request to get public session info: sessionId={}", conversationId);
        return chatService.getPublicSessionInfoById(conversationId);
    }
    
    /**
     * 更新会话信息
     * @param request 更新会话请求体
     * @return Response 操作结果
     */
    @Operation(summary = "更新会话信息", description = "更新指定会话的音频ID、图片风格ID和图片大小等信息")
    @PostMapping("/session/update")
    public Response updateSession(@Parameter(description = "更新会话请求体", required = true) @RequestBody UpdateSessionReq request) {
        log.info("Received request to update session info: {}", request);
        return chatService.updateSession(request);
    }
    
    /**
     * 公开接口：更新会话信息（无需用户登录）
     * @param request 更新会话请求体
     * @return Response 操作结果
     */
    @IgnoreRequestUser
    @Operation(summary = "公开接口：更新会话信息", description = "更新指定会话的音频ID、图片风格ID和图片大小等信息，无需用户登录")
    @PostMapping("/public/session/update")
    public Response updatePublicSession(@Parameter(description = "更新会话请求体", required = true) @RequestBody UpdateSessionReq request) {
        log.info("Received request to update public session info: {}", request);
        return chatService.updatePublicSession(request);
    }

    /**
     * 提示词优化接口
     * @param request 提示词优化请求对象
     * @return 优化后的提示词响应对象
     */
    @Operation(summary = "提示词优化", description = "通过请求体输入原始提示词和图片URL，通过Dify API优化后返回优化的提示词")
    @PostMapping("/optimize-prompt")
    public SingleResponse<PromptOptimizeRes> optimizePromptWithBody(
            @Parameter(description = "提示词优化请求参数", required = true)
            @Valid @RequestBody PromptOptimizeReq request) {
        log.info("接收到提示词优化请求: prompt={}, imageUrls={}",
                request.getPrompt(), request.getImageUrls());
        return SingleResponse.of(chatService.optimizePrompt(request.getPrompt(), request.getImageUrls()));
    }
}
