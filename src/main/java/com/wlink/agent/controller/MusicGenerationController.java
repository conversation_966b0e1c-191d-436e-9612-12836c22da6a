package com.wlink.agent.controller;

import com.wlink.agent.client.model.music.FalQueueStatus;
import com.wlink.agent.client.model.music.Lyria2Input;
import com.wlink.agent.client.model.music.Lyria2Output;
import com.wlink.agent.dao.po.AiMusicGenerationRecordPo;
import com.wlink.agent.service.MusicGenerationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 音乐生成控制器
 * 提供音乐生成相关的REST API接口
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-04
 */
@Slf4j
@RestController
@RequestMapping("/api/music")
@RequiredArgsConstructor
@Tag(name = "音乐生成", description = "基于Fal AI Lyria2的音乐生成API")
public class MusicGenerationController {

    private final MusicGenerationService musicGenerationService;

    /**
     * 同步生成音乐
     */
    @PostMapping("/generate")
    @Operation(summary = "同步生成音乐", description = "提交音乐生成请求并等待完成，返回生成的音乐文件")
    public ResponseEntity<Lyria2Output> generateMusic(
            @Valid @RequestBody Lyria2Input input) {
        
        log.info("收到音乐生成请求: prompt={}", input.getPrompt());
        
        try {
            Lyria2Output result = musicGenerationService.generateMusic(input);
            log.info("音乐生成成功: audioUrl={}", result.getAudioUrl());
            return ResponseEntity.ok(result);
            
        } catch (IOException e) {
            log.error("音乐生成失败: prompt={}, error={}", input.getPrompt(), e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        } catch (IllegalArgumentException e) {
            log.warn("音乐生成参数无效: prompt={}, error={}", input.getPrompt(), e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 异步生成音乐
     */
    @PostMapping("/generate/async")
    @Operation(summary = "异步生成音乐", description = "提交音乐生成请求，立即返回任务信息，需要后续查询结果")
    public ResponseEntity<FalQueueStatus> generateMusicAsync(
            @Valid @RequestBody Lyria2Input input) {
        
        log.info("收到异步音乐生成请求: prompt={}", input.getPrompt());
        
        try {
            FalQueueStatus status = musicGenerationService.submitMusicGenerationRequest(input);
            log.info("异步音乐生成请求已提交: requestId={}, status={}", 
                    status.getRequestId(), status.getStatus());
            return ResponseEntity.ok(status);
            
        } catch (IOException e) {
            log.error("异步音乐生成请求失败: prompt={}, error={}", input.getPrompt(), e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        } catch (IllegalArgumentException e) {
            log.warn("异步音乐生成参数无效: prompt={}, error={}", input.getPrompt(), e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 根据提示词生成音乐（简化接口）
     */
    @PostMapping("/generate/simple")
    @Operation(summary = "简单音乐生成", description = "根据文本提示词生成音乐，使用默认参数")
    public ResponseEntity<Lyria2Output> generateMusicByPrompt(
            @Parameter(description = "音乐描述提示词", required = true)
            @RequestParam String prompt) {
        
        log.info("收到简单音乐生成请求: prompt={}", prompt);
        
        try {
            Lyria2Output result = musicGenerationService.generateMusicByPrompt(prompt);
            log.info("简单音乐生成成功: audioUrl={}", result.getAudioUrl());
            return ResponseEntity.ok(result);
            
        } catch (IOException e) {
            log.error("简单音乐生成失败: prompt={}, error={}", prompt, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        } catch (IllegalArgumentException e) {
            log.warn("简单音乐生成参数无效: prompt={}, error={}", prompt, e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 查询任务状态
     */
    @GetMapping("/task/{requestId}/status")
    @Operation(summary = "查询任务状态", description = "根据请求ID查询音乐生成任务的当前状态")
    public ResponseEntity<FalQueueStatus> getTaskStatus(
            @Parameter(description = "请求ID", required = true)
            @PathVariable String requestId) {
        
        log.debug("查询任务状态: requestId={}", requestId);
        
        try {
            FalQueueStatus status = musicGenerationService.getTaskStatus(requestId);
            log.debug("任务状态查询成功: requestId={}, status={}", requestId, status.getStatus());
            return ResponseEntity.ok(status);
            
        } catch (IOException e) {
            log.error("查询任务状态失败: requestId={}, error={}", requestId, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 获取任务结果
     */
    @GetMapping("/task/{requestId}/result")
    @Operation(summary = "获取任务结果", description = "根据请求ID获取已完成任务的音乐生成结果")
    public ResponseEntity<Lyria2Output> getTaskResult(
            @Parameter(description = "请求ID", required = true)
            @PathVariable String requestId) {
        
        log.info("获取任务结果: requestId={}", requestId);
        
        try {
            Lyria2Output result = musicGenerationService.getTaskResult(requestId);
            log.info("任务结果获取成功: requestId={}, audioUrl={}", requestId, result.getAudioUrl());
            return ResponseEntity.ok(result);
            
        } catch (IOException e) {
            log.error("获取任务结果失败: requestId={}, error={}", requestId, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 等待任务完成
     */
    @GetMapping("/task/{requestId}/wait")
    @Operation(summary = "等待任务完成", description = "等待指定任务完成并返回结果，可能需要较长时间")
    public ResponseEntity<Lyria2Output> waitForTaskCompletion(
            @Parameter(description = "请求ID", required = true)
            @PathVariable String requestId) {
        
        log.info("等待任务完成: requestId={}", requestId);
        
        try {
            Lyria2Output result = musicGenerationService.waitForTaskCompletion(requestId);
            log.info("任务等待完成: requestId={}, audioUrl={}", requestId, result.getAudioUrl());
            return ResponseEntity.ok(result);
            
        } catch (IOException e) {
            log.error("等待任务完成失败: requestId={}, error={}", requestId, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 取消任务
     */
    @DeleteMapping("/task/{requestId}")
    @Operation(summary = "取消任务", description = "取消指定的音乐生成任务")
    public ResponseEntity<Void> cancelTask(
            @Parameter(description = "请求ID", required = true)
            @PathVariable String requestId) {
        
        log.info("取消任务: requestId={}", requestId);
        
        try {
            boolean cancelled = musicGenerationService.cancelTask(requestId);
            
            if (cancelled) {
                log.info("任务取消成功: requestId={}", requestId);
                return ResponseEntity.ok().build();
            } else {
                log.warn("任务取消失败: requestId={}", requestId);
                return ResponseEntity.badRequest().build();
            }
            
        } catch (IOException e) {
            log.error("取消任务失败: requestId={}, error={}", requestId, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 带种子值生成音乐
     */
    @PostMapping("/generate/with-seed")
    @Operation(summary = "带种子值生成音乐", description = "使用指定种子值生成音乐，确保结果可重现")
    public ResponseEntity<Lyria2Output> generateMusicWithSeed(
            @Parameter(description = "音乐描述提示词", required = true)
            @RequestParam String prompt,
            @Parameter(description = "种子值，用于确定性生成")
            @RequestParam(required = false) Integer seed) {
        
        log.info("收到带种子值音乐生成请求: prompt={}, seed={}", prompt, seed);
        
        try {
            Lyria2Output result = musicGenerationService.generateMusicWithSeed(prompt, seed);
            log.info("带种子值音乐生成成功: audioUrl={}", result.getAudioUrl());
            return ResponseEntity.ok(result);
            
        } catch (IOException e) {
            log.error("带种子值音乐生成失败: prompt={}, seed={}, error={}", prompt, seed, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        } catch (IllegalArgumentException e) {
            log.warn("带种子值音乐生成参数无效: prompt={}, seed={}, error={}", prompt, seed, e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    // ==================== 数据库查询接口 ====================

    /**
     * 查询音乐生成记录
     */
    @GetMapping("/record/{requestId}")
    @Operation(summary = "查询音乐生成记录", description = "根据请求ID查询音乐生成记录详情")
    public ResponseEntity<AiMusicGenerationRecordPo> getMusicGenerationRecord(
            @Parameter(description = "请求ID", required = true)
            @PathVariable String requestId) {

        log.debug("查询音乐生成记录: requestId={}", requestId);

        try {
            AiMusicGenerationRecordPo record = musicGenerationService.getMusicGenerationRecord(requestId);

            if (record != null) {
                log.debug("查询音乐生成记录成功: requestId={}, status={}", requestId, record.getStatus());
                return ResponseEntity.ok(record);
            } else {
                log.warn("音乐生成记录不存在: requestId={}", requestId);
                return ResponseEntity.notFound().build();
            }

        } catch (Exception e) {
            log.error("查询音乐生成记录失败: requestId={}, error={}", requestId, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 查询会话的音乐生成记录列表
     */
    @GetMapping("/records/session/{sessionId}")
    @Operation(summary = "查询会话音乐记录", description = "根据会话ID查询该会话的所有音乐生成记录")
    public ResponseEntity<List<AiMusicGenerationRecordPo>> getMusicGenerationRecordsBySession(
            @Parameter(description = "会话ID", required = true)
            @PathVariable String sessionId) {

        log.debug("查询会话音乐生成记录: sessionId={}", sessionId);

        try {
            List<AiMusicGenerationRecordPo> records = musicGenerationService.getMusicGenerationRecordsBySession(sessionId);
            log.debug("查询会话音乐生成记录成功: sessionId={}, count={}", sessionId, records.size());
            return ResponseEntity.ok(records);

        } catch (Exception e) {
            log.error("查询会话音乐生成记录失败: sessionId={}, error={}", sessionId, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 查询会话章节的音乐生成记录列表
     */
    @GetMapping("/records/session/{sessionId}/segment/{segmentId}")
    @Operation(summary = "查询章节音乐记录", description = "根据会话ID和章节ID查询音乐生成记录")
    public ResponseEntity<List<AiMusicGenerationRecordPo>> getMusicGenerationRecordsBySessionAndSegment(
            @Parameter(description = "会话ID", required = true)
            @PathVariable String sessionId,
            @Parameter(description = "章节ID", required = true)
            @PathVariable String segmentId) {

        log.debug("查询会话章节音乐生成记录: sessionId={}, segmentId={}", sessionId, segmentId);

        try {
            List<AiMusicGenerationRecordPo> records =
                musicGenerationService.getMusicGenerationRecordsBySessionAndSegment(sessionId, segmentId);
            log.debug("查询会话章节音乐生成记录成功: sessionId={}, segmentId={}, count={}",
                     sessionId, segmentId, records.size());
            return ResponseEntity.ok(records);

        } catch (Exception e) {
            log.error("查询会话章节音乐生成记录失败: sessionId={}, segmentId={}, error={}",
                     sessionId, segmentId, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 查询用户的音乐生成记录列表
     */
    @GetMapping("/records/user/{userId}")
    @Operation(summary = "查询用户音乐记录", description = "根据用户ID查询音乐生成记录")
    public ResponseEntity<List<AiMusicGenerationRecordPo>> getMusicGenerationRecordsByUser(
            @Parameter(description = "用户ID", required = true)
            @PathVariable String userId,
            @Parameter(description = "限制数量")
            @RequestParam(required = false, defaultValue = "20") Integer limit) {

        log.debug("查询用户音乐生成记录: userId={}, limit={}", userId, limit);

        try {
            List<AiMusicGenerationRecordPo> records = musicGenerationService.getMusicGenerationRecordsByUser(userId, limit);
            log.debug("查询用户音乐生成记录成功: userId={}, count={}", userId, records.size());
            return ResponseEntity.ok(records);

        } catch (Exception e) {
            log.error("查询用户音乐生成记录失败: userId={}, error={}", userId, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 统计用户正在处理中的任务数量
     */
    @GetMapping("/stats/user/{userId}/processing")
    @Operation(summary = "统计用户处理中任务", description = "统计用户正在处理中的音乐生成任务数量")
    public ResponseEntity<Integer> countUserProcessingTasks(
            @Parameter(description = "用户ID", required = true)
            @PathVariable String userId) {

        log.debug("统计用户处理中任务: userId={}", userId);

        try {
            int count = musicGenerationService.countUserProcessingTasks(userId);
            log.debug("统计用户处理中任务成功: userId={}, count={}", userId, count);
            return ResponseEntity.ok(count);

        } catch (Exception e) {
            log.error("统计用户处理中任务失败: userId={}, error={}", userId, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 统计系统正在处理中的任务数量
     */
    @GetMapping("/stats/processing")
    @Operation(summary = "统计系统处理中任务", description = "统计系统正在处理中的音乐生成任务总数")
    public ResponseEntity<Integer> countProcessingTasks() {

        log.debug("统计系统处理中任务");

        try {
            int count = musicGenerationService.countProcessingTasks();
            log.debug("统计系统处理中任务成功: count={}", count);
            return ResponseEntity.ok(count);

        } catch (Exception e) {
            log.error("统计系统处理中任务失败: error={}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }
}
