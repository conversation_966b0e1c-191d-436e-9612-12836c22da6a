package com.wlink.agent.dao.dto;

import com.wlink.agent.dao.po.AiMusicGenerationRecordPo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 音乐生成记录DTO
 * 用于API响应，隐藏内部字段
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-04
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MusicGenerationRecordDto {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 章节ID
     */
    private String segmentId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * Fal AI请求ID
     */
    private String requestId;

    /**
     * 音乐生成提示词
     */
    private String prompt;

    /**
     * 负面提示词
     */
    private String negativePrompt;

    /**
     * 种子值
     */
    private Integer seed;

    /**
     * 生成状态
     */
    private String status;

    /**
     * 队列位置
     */
    private Integer queuePosition;

    /**
     * 生成的音乐文件URL
     */
    private String musicUrl;

    /**
     * 音乐文件名
     */
    private String musicFileName;

    /**
     * 音乐文件大小（字节）
     */
    private Long musicFileSize;

    /**
     * 音乐文件MIME类型
     */
    private String musicContentType;

    /**
     * 音乐时长（毫秒）
     */
    private Long musicDuration;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 消耗积分
     */
    private Integer consumePoints;

    /**
     * 生成开始时间
     */
    private Date generationStartTime;

    /**
     * 生成完成时间
     */
    private Date generationEndTime;

    /**
     * 生成耗时（毫秒）
     */
    private Long generationCostTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 从PO对象转换为DTO
     * 
     * @param po PO对象
     * @return DTO对象
     */
    public static MusicGenerationRecordDto fromPo(AiMusicGenerationRecordPo po) {
        if (po == null) {
            return null;
        }
        
        return MusicGenerationRecordDto.builder()
                .id(po.getId())
                .sessionId(po.getSessionId())
                .segmentId(po.getSegmentId())
                .userId(po.getUserId())
                .requestId(po.getRequestId())
                .prompt(po.getPrompt())
                .negativePrompt(po.getNegativePrompt())
                .seed(po.getSeed())
                .status(po.getStatus())
                .queuePosition(po.getQueuePosition())
                .musicUrl(po.getMusicUrl())
                .musicFileName(po.getMusicFileName())
                .musicFileSize(po.getMusicFileSize())
                .musicContentType(po.getMusicContentType())
                .musicDuration(po.getMusicDuration())
                .errorMessage(po.getErrorMessage())
                .consumePoints(po.getConsumePoints())
                .generationStartTime(po.getGenerationStartTime())
                .generationEndTime(po.getGenerationEndTime())
                .generationCostTime(po.getGenerationCostTime())
                .createTime(po.getCreateTime())
                .updateTime(po.getUpdateTime())
                .build();
    }

    /**
     * 检查是否已完成
     */
    public boolean isCompleted() {
        return AiMusicGenerationRecordPo.Status.COMPLETED.equals(status);
    }

    /**
     * 检查是否失败
     */
    public boolean isFailed() {
        return AiMusicGenerationRecordPo.Status.FAILED.equals(status);
    }

    /**
     * 检查是否正在处理中
     */
    public boolean isProcessing() {
        return AiMusicGenerationRecordPo.Status.IN_PROGRESS.equals(status) || 
               AiMusicGenerationRecordPo.Status.IN_QUEUE.equals(status);
    }

    /**
     * 获取格式化的文件大小
     */
    public String getFormattedFileSize() {
        if (musicFileSize == null || musicFileSize <= 0) {
            return "未知大小";
        }
        
        if (musicFileSize < 1024) {
            return musicFileSize + " B";
        } else if (musicFileSize < 1024 * 1024) {
            return String.format("%.1f KB", musicFileSize / 1024.0);
        } else if (musicFileSize < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", musicFileSize / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", musicFileSize / (1024.0 * 1024.0 * 1024.0));
        }
    }

    /**
     * 获取格式化的音乐时长
     */
    public String getFormattedDuration() {
        if (musicDuration == null || musicDuration <= 0) {
            return "未知时长";
        }
        
        long seconds = musicDuration / 1000;
        long minutes = seconds / 60;
        seconds = seconds % 60;
        
        if (minutes > 0) {
            return String.format("%d分%d秒", minutes, seconds);
        } else {
            return String.format("%d秒", seconds);
        }
    }

    /**
     * 获取状态描述
     */
    public String getStatusDescription() {
        if (status == null) {
            return "未知状态";
        }
        
        switch (status) {
            case AiMusicGenerationRecordPo.Status.PENDING:
                return "等待中";
            case AiMusicGenerationRecordPo.Status.IN_QUEUE:
                return queuePosition != null ? 
                    String.format("队列中 (位置: %d)", queuePosition) : "队列中";
            case AiMusicGenerationRecordPo.Status.IN_PROGRESS:
                return "处理中";
            case AiMusicGenerationRecordPo.Status.COMPLETED:
                return "已完成";
            case AiMusicGenerationRecordPo.Status.FAILED:
                return "失败";
            case AiMusicGenerationRecordPo.Status.CANCELLED:
                return "已取消";
            default:
                return "未知状态";
        }
    }
}
