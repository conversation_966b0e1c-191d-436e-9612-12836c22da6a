package com.wlink.agent.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wlink.agent.dao.po.AiMusicGenerationRecordPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 音乐生成记录表Mapper接口
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-04
 */
@Mapper
public interface AiMusicGenerationRecordMapper extends BaseMapper<AiMusicGenerationRecordPo> {

    /**
     * 根据请求ID查询记录
     * 
     * @param requestId 请求ID
     * @return 音乐生成记录
     */
    AiMusicGenerationRecordPo selectByRequestId(@Param("requestId") String requestId);

    /**
     * 根据会话ID查询记录列表
     * 
     * @param sessionId 会话ID
     * @return 音乐生成记录列表
     */
    List<AiMusicGenerationRecordPo> selectBySessionId(@Param("sessionId") String sessionId);

    /**
     * 根据会话ID和章节ID查询记录列表
     * 
     * @param sessionId 会话ID
     * @param segmentId 章节ID
     * @return 音乐生成记录列表
     */
    List<AiMusicGenerationRecordPo> selectBySessionIdAndSegmentId(@Param("sessionId") String sessionId, 
                                                                  @Param("segmentId") String segmentId);

    /**
     * 根据用户ID查询记录列表
     * 
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 音乐生成记录列表
     */
    List<AiMusicGenerationRecordPo> selectByUserId(@Param("userId") String userId, 
                                                   @Param("limit") Integer limit);

    /**
     * 根据状态查询记录列表
     * 
     * @param status 状态
     * @param limit 限制数量
     * @return 音乐生成记录列表
     */
    List<AiMusicGenerationRecordPo> selectByStatus(@Param("status") String status, 
                                                   @Param("limit") Integer limit);

    /**
     * 查询正在处理中的任务数量
     * 
     * @return 处理中的任务数量
     */
    int countProcessingTasks();

    /**
     * 查询用户正在处理中的任务数量
     * 
     * @param userId 用户ID
     * @return 用户处理中的任务数量
     */
    int countUserProcessingTasks(@Param("userId") String userId);

    /**
     * 查询超时的处理中任务
     * 
     * @param timeoutMinutes 超时分钟数
     * @return 超时任务列表
     */
    List<AiMusicGenerationRecordPo> selectTimeoutProcessingTasks(@Param("timeoutMinutes") Integer timeoutMinutes);

    /**
     * 批量更新任务状态
     * 
     * @param requestIds 请求ID列表
     * @param status 新状态
     * @param errorMessage 错误信息（可选）
     * @return 更新数量
     */
    int batchUpdateStatus(@Param("requestIds") List<String> requestIds, 
                         @Param("status") String status, 
                         @Param("errorMessage") String errorMessage);

    /**
     * 统计用户的音乐生成数量
     * 
     * @param userId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 生成数量
     */
    int countUserGenerations(@Param("userId") String userId, 
                            @Param("startTime") String startTime, 
                            @Param("endTime") String endTime);

    /**
     * 查询最近的成功生成记录
     * 
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 成功生成记录列表
     */
    List<AiMusicGenerationRecordPo> selectRecentSuccessRecords(@Param("userId") String userId, 
                                                               @Param("limit") Integer limit);

    /**
     * 根据会话ID删除记录（逻辑删除）
     * 
     * @param sessionId 会话ID
     * @return 删除数量
     */
    int deleteBySessionId(@Param("sessionId") String sessionId);

    /**
     * 根据会话ID和章节ID删除记录（逻辑删除）
     * 
     * @param sessionId 会话ID
     * @param segmentId 章节ID
     * @return 删除数量
     */
    int deleteBySessionIdAndSegmentId(@Param("sessionId") String sessionId, 
                                     @Param("segmentId") String segmentId);
}
