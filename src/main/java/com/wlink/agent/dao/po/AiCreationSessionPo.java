package com.wlink.agent.dao.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * AI创作会话
 * @TableName ai_creation_session
 */
@TableName(value="ai_creation_session")
@Data
public class AiCreationSessionPo implements Serializable {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 会话id
     */
    private String sessionId;

    /**
     * 用户ID
     */

    private String userId;

    /**
     * dify会话id
     */

    private String conversationId;


    /**
     * dify 任务id
     */
    private String taskId;

    /**
     * 创作标题
     */
    private String title;


    /**
     * 描述
     */
    private String description;

    /**
     * 创作提示
     */
    private String prompt;


    private Long soundId;


    private Long imageStyleId;


    private String imageSize;

    /**
     * 图片地址
     */
    private String imageUrl;


    private String imageModel;

    /**
     * 参考图数量
     */
    private Integer referenceImageCount;

    /***/
    private Integer status;

    /**
     * Token使用
     */

    private Integer tokenUsage;

    /**
     * 创建时间
     */

    private Date createTime;

    /**
     * 更新时间
     */

    private Date updateTime;

    /**
     * 删除标记(0-正常,1-删除)
     */
    @TableLogic
    private Integer delFlag;

    private static final long serialVersionUID = 1L;

}
