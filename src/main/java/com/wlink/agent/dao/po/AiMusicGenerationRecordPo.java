package com.wlink.agent.dao.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 音乐生成记录表
 * @TableName ai_music_generation_record
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-04
 */
@TableName(value = "ai_music_generation_record")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AiMusicGenerationRecordPo implements Serializable {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 会话ID
     */
    @TableField("session_id")
    private String sessionId;

    /**
     * 章节ID
     */
    @TableField("segment_id")
    private String segmentId;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * Fal AI请求ID
     */
    @TableField("request_id")
    private String requestId;

    /**
     * 音乐生成提示词
     */
    @TableField("prompt")
    private String prompt;

    /**
     * 负面提示词
     */
    @TableField("negative_prompt")
    private String negativePrompt;

    /**
     * 种子值，用于确定性生成
     */
    @TableField("seed")
    private Integer seed;

    /**
     * 生成状态
     * PENDING-等待中, IN_QUEUE-队列中, IN_PROGRESS-处理中, 
     * COMPLETED-已完成, FAILED-失败, CANCELLED-已取消
     */
    @TableField("status")
    private String status;

    /**
     * 队列位置
     */
    @TableField("queue_position")
    private Integer queuePosition;

    /**
     * 生成的音乐文件URL
     */
    @TableField("music_url")
    private String musicUrl;

    /**
     * 音乐文件名
     */
    @TableField("music_file_name")
    private String musicFileName;

    /**
     * 音乐文件大小（字节）
     */
    @TableField("music_file_size")
    private Long musicFileSize;

    /**
     * 音乐文件MIME类型
     */
    @TableField("music_content_type")
    private String musicContentType;

    /**
     * 音乐时长（毫秒）
     */
    @TableField("music_duration")
    private Long musicDuration;

    /**
     * 错误信息
     */
    @TableField("error_message")
    private String errorMessage;

    /**
     * 消耗积分
     */
    @TableField("consume_points")
    private Integer consumePoints;

    /**
     * 生成开始时间
     */
    @TableField("generation_start_time")
    private Date generationStartTime;

    /**
     * 生成完成时间
     */
    @TableField("generation_end_time")
    private Date generationEndTime;

    /**
     * 生成耗时（毫秒）
     */
    @TableField("generation_cost_time")
    private Long generationCostTime;

    /**
     * 完整请求参数（JSON格式）
     */
    @TableField("request_params")
    private String requestParams;

    /**
     * 完整响应数据（JSON格式）
     */
    @TableField("response_data")
    private String responseData;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 删除标记(0-正常,1-删除)
     */
    @TableLogic
    @TableField("del_flag")
    private Integer delFlag;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 状态枚举
     */
    public static class Status {
        public static final String PENDING = "PENDING";
        public static final String IN_QUEUE = "IN_QUEUE";
        public static final String IN_PROGRESS = "IN_PROGRESS";
        public static final String COMPLETED = "COMPLETED";
        public static final String FAILED = "FAILED";
        public static final String CANCELLED = "CANCELLED";
    }

    /**
     * 检查是否已完成
     */
    public boolean isCompleted() {
        return Status.COMPLETED.equals(status);
    }

    /**
     * 检查是否失败
     */
    public boolean isFailed() {
        return Status.FAILED.equals(status);
    }

    /**
     * 检查是否正在处理中
     */
    public boolean isProcessing() {
        return Status.IN_PROGRESS.equals(status) || Status.IN_QUEUE.equals(status);
    }

    /**
     * 检查是否可以取消
     */
    public boolean isCancellable() {
        return Status.PENDING.equals(status) || Status.IN_QUEUE.equals(status) || Status.IN_PROGRESS.equals(status);
    }

    /**
     * 计算生成耗时
     */
    public void calculateCostTime() {
        if (generationStartTime != null && generationEndTime != null) {
            this.generationCostTime = generationEndTime.getTime() - generationStartTime.getTime();
        }
    }

    /**
     * 获取格式化的文件大小
     */
    public String getFormattedFileSize() {
        if (musicFileSize == null || musicFileSize <= 0) {
            return "未知大小";
        }
        
        if (musicFileSize < 1024) {
            return musicFileSize + " B";
        } else if (musicFileSize < 1024 * 1024) {
            return String.format("%.1f KB", musicFileSize / 1024.0);
        } else if (musicFileSize < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", musicFileSize / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", musicFileSize / (1024.0 * 1024.0 * 1024.0));
        }
    }

    /**
     * 获取格式化的音乐时长
     */
    public String getFormattedDuration() {
        if (musicDuration == null || musicDuration <= 0) {
            return "未知时长";
        }
        
        long seconds = musicDuration / 1000;
        long minutes = seconds / 60;
        seconds = seconds % 60;
        
        if (minutes > 0) {
            return String.format("%d分%d秒", minutes, seconds);
        } else {
            return String.format("%d秒", seconds);
        }
    }
}
