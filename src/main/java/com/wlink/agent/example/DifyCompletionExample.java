package com.wlink.agent.example;

import com.wlink.agent.model.res.PromptOptimizeRes;
import com.wlink.agent.service.ChatService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * Dify完成消息API使用示例
 * 
 * 启用示例：在配置文件中添加 dify.completion.example.enabled=true
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(name = "dify.completion.example.enabled", havingValue = "true")
public class DifyCompletionExample implements CommandLineRunner {

    private final ChatService chatService;

    @Override
    public void run(String... args) throws Exception {
        log.info("=== Dify完成消息API示例开始 ===");
        
        try {
            // 示例1：提示词优化（无图片）
            log.info("示例1：提示词优化（无图片）");
            String simplePrompt = "生成一个游戏广告";
            PromptOptimizeRes optimizeResult = chatService.optimizePrompt(simplePrompt);
            if (optimizeResult != null && optimizeResult.getOptimizedPrompt() != null) {
                log.info("原始提示词: {}", simplePrompt);
                log.info("优化后提示词: {}", optimizeResult.getOptimizedPrompt());
            } else {
                log.warn("提示词优化失败");
            }

            // 示例2：提示词优化（带图片）
            log.info("\n示例2：提示词优化（带图片）");
            String imagePrompt = "根据图中的人物，创作一个故事";
            List<String> imageUrls = Arrays.asList(
                "https://wlpaas.weilitech.cn/dify/prod/1927918453497950208/image/98e6e550a81441d58e42b6b68f8ba0e4.png"
            );
            PromptOptimizeRes imageOptimizeResult = chatService.optimizePrompt(imagePrompt, imageUrls);
            if (imageOptimizeResult != null && imageOptimizeResult.getOptimizedPrompt() != null) {
                log.info("原始提示词: {}", imagePrompt);
                log.info("图片URLs: {}", imageUrls);
                log.info("优化后提示词: {}", imageOptimizeResult.getOptimizedPrompt());
            } else {
                log.warn("带图片的提示词优化失败");
            }
            
        } catch (Exception e) {
            log.error("示例执行失败", e);
        }
        
        log.info("=== Dify完成消息API示例结束 ===");
    }
}
