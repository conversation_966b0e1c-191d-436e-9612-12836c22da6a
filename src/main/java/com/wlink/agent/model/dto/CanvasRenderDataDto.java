package com.wlink.agent.model.dto;

import lombok.Data;

import java.util.List;

/**
 * 画布渲染数据DTO
 * 用于生成发送给Python渲染接口的JSON数据
 */
@Data
public class CanvasRenderDataDto {

    /**
     * 画布ID
     */
    private Long canvasId;

    /**
     * 画布名称
     */
    private String canvasName;

    /**
     * 分辨率
     */
    private String resolution;

    /**
     * 帧率
     */
    private Integer fps;

    /**
     * 比例
     */
    private String ratio;

    /**
     * 是否显示字幕(0-不显示,1-显示)
     */
    private Integer showSubtitle;

    /**
     * 分镜列表
     */
    private List<ShotRenderDataDto> shots;

    /**
     * 背景音乐数据
     */
    private BackgroundMusicRenderDataDto backgroundMusic;

    /**
     * 分镜渲染数据DTO
     */
    @Data
    public static class ShotRenderDataDto {
        
        /**
         * 分镜编码
         */
        private String code;
        
        /**
         * 分镜类型 (image/video)
         */
        private String type;
        
        /**
         * 排序序号
         */
        private Integer sortOrder;
        
        /**
         * 构图描述
         */
        private String composition;
        
        /**
         * 显示类型
         */
        private String displayType;
        
        /**
         * 运动类型
         */
        private String movement;

        /**
         * 播放时长
         */
        private Long playDuration;

        /**
         * 图片数据 (当type为image时)
         */
        private ImageRenderDataDto imageData;
        
        /**
         * 视频数据 (当type为video时)
         */
        private VideoRenderDataDto videoData;
        
        /**
         * 音频列表 (按sortOrder排序)
         */
        private List<AudioRenderDataDto> audios;
    }

    /**
     * 图片渲染数据DTO
     */
    @Data
    public static class ImageRenderDataDto {
        
        /**
         * 图片URL
         */
        private String imageUrl;
        /**
         * 图片宽高比
         */
        private String imageAspectRatio;
        
        /**
         * 图片状态
         */
        private String imageStatus;
        
        /**
         * 参考图片URL
         */
        private String referenceImage;
    }

    /**
     * 视频渲染数据DTO
     */
    @Data
    public static class VideoRenderDataDto {
        
        /**
         * 视频URL
         */
        private String videoUrl;
        /**
         * 视频时长(毫秒)
         */
        private Integer videoDuration;
        
        /**
         * 视频宽高比
         */
        private String videoAspectRatio;
        
        /**
         * 视频状态
         */
        private String videoStatus;
        
        /**
         * 开始帧图片URL
         */
        private String startFrameImage;
        
        /**
         * 结束帧图片URL
         */
        private String endFrameImage;

        /**
         * 视频音量(0.00-1.00)
         */
        private java.math.BigDecimal volume;
    }

    /**
     * 音频渲染数据DTO
     */
    @Data
    public static class AudioRenderDataDto {
        
        /**
         * 音频URL
         */
        private String audioUrl;
        
        /**
         * 音频类型(1-旁白,2-对话,3-背景音乐)
         */
        private Integer audioType;
        
        /**
         * 音频文本
         */
        private String text;
        
        /**
         * 声音ID
         */
        private String voiceId;
        
        /**
         * 音频时长(毫秒)
         */
        private Long audioDuration;
        
        /**
         * 排序序号
         */
        private Integer sortOrder;

        /**
         * 音频音量(0.00-1.00)
         */
        private java.math.BigDecimal volume;
    }

    /**
     * 背景音乐渲染数据DTO
     */
    @Data
    public static class BackgroundMusicRenderDataDto {

        /**
         * 音频URL
         */
        private String audioUrl;

        /**
         * 音频名称
         */
        private String name;

        /**
         * 音频时长（毫秒）
         */
        private Long audioDuration;

        /**
         * 开始播放时间（毫秒）
         */
        private Long startTime;

        /**
         * 结束播放时间（毫秒）
         */
        private Long endTime;

        /**
         * 音轨开始时间（毫秒）
         */
        private Long startTrackTime;

        /**
         * 音量（0-100）
         */
        private Double volume;

        /**
         * 淡入时间（毫秒）
         */
        private Long fadeInTime;

        /**
         * 淡出时间（毫秒）
         */
        private Long fadeOutTime;

        /**
         * 是否循环播放（0-否，1-是）
         */
        private Integer isLoop;

        /**
         * 音频格式
         */
        private String audioFormat;

    }
}
