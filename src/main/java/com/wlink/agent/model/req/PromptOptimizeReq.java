package com.wlink.agent.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import java.util.List;

/**
 * 提示词优化请求
 */
@Data
@Schema(description = "提示词优化请求")
public class PromptOptimizeReq {

    /**
     * 原始提示词
     */
    @NotBlank(message = "提示词不能为空")
    @Schema(description = "原始提示词", required = true, example = "生成一个游戏广告")
    private String prompt;

    /**
     * 图片URL集合
     */
    @Schema(description = "图片URL集合", example = "[\"https://wlpaas.weilitech.cn/dify/prod/1927918453497950208/image/98e6e550a81441d58e42b6b68f8ba0e4.png\"]")
    private List<String> imageUrls;
}
