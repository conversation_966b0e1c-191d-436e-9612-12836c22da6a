package com.wlink.agent.model.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * 更新会话信息请求
 */
@Data
@Schema(description = "更新会话信息请求")
public class UpdateSessionReq {
    
    @NotBlank(message = "会话ID不能为空")
    @Schema(description = "会话ID", required = true, example = "sess_123456789")
    private String conversationId;
    
    @Schema(description = "音频ID", example = "123456789")
    private Long soundId;
    
    @Schema(description = "图片风格ID", example = "123456789")
    private Long imageStyleId;
    
    @Schema(description = "图片大小比例", example = "16:9")
    private String imageSize;

    @Schema(description = "参考图数量", example = "3")
    private Integer referenceImageCount;

    @Schema(description = "章节提示词对象")
    private ChapterPrompt chapterPrompt;

    /**
     * 章节提示词对象
     */
    @Data
    @Schema(description = "章节提示词对象")
    public static class ChapterPrompt {
        @Schema(description = "章节ID", required = true, example = "chapter_123456789")
        private String segmentId;

        @Schema(description = "章节提示词", required = true, example = "This is a chapter prompt")
        private String prompt;
    }


} 
