package com.wlink.agent.model.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 提示词优化响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "提示词优化响应")
public class PromptOptimizeRes {

    /**
     * 优化后的提示词
     */
    @Schema(description = "优化后的提示词", example = "风格化3D卡通画面，色彩明亮，皮克斯动画电影质感。我需要一个抖音投放的快节奏游戏广告...")
    private String optimizedPrompt;

    /**
     * 创建响应对象的便捷方法
     *
     * @param optimizedPrompt 优化后的提示词
     * @return 提示词优化响应对象
     */
    public static PromptOptimizeRes of(String optimizedPrompt) {
        return PromptOptimizeRes.builder()
                .optimizedPrompt(optimizedPrompt)
                .build();
    }
}
