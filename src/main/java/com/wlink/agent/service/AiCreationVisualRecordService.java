package com.wlink.agent.service;

import com.alibaba.cola.dto.PageResponse;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wlink.agent.common.dto.PageRes;
import com.wlink.agent.dao.po.AiCreationVisualRecordPo;
import com.wlink.agent.model.req.RenderTriggerReq;
import com.wlink.agent.model.req.UpdateVisualRecordStatusReq;
import com.wlink.agent.model.req.VisualSaveReq;
import com.wlink.agent.model.res.AiCreationVisualRecordListRes;
import com.wlink.agent.model.res.AiCreationVisualRecordRes;
import com.wlink.agent.model.res.SessionReferenceImageRes;
import com.wlink.agent.model.res.VisualRecordPageRes;

import java.util.List;

/**
 * AI创作视觉记录服务接口
 */
public interface AiCreationVisualRecordService extends IService<AiCreationVisualRecordPo> {
    
    /**
     * 保存视觉记录
     * @param conversationId 会话ID
     * @param visualData 视觉数据
     * @param userId 用户ID
     * @return 视觉记录ID
     */
    Long saveVisualRecord(String conversationId, VisualSaveReq visualData, String userId);
    
    /**
     * 更新视觉记录的发布状态
     * @param req 请求 DTO
     * @return 分享令牌 (仅在发布时返回，取消发布时返回null)
     */
    String updatePublishStatus(UpdateVisualRecordStatusReq req);
    
    /**
     * 查询会话的所有视觉记录
     * @param conversationId 会话ID
     * @return 视觉记录列表 (Res DTO)
     */
    List<AiCreationVisualRecordRes> getVisualRecordsBySession(String conversationId);
    
    /**
     * 根据分享令牌获取视觉记录
     * @param shareToken 分享令牌
     * @return 视觉记录 (Res DTO)
     */
    AiCreationVisualRecordRes getVisualRecordByShareToken(String shareToken);

    /**
     * 根据ID获取视觉记录（包含权限检查）
     * @param visualRecordId 记录ID
     * @return 视觉记录 (Res DTO)
     */
    AiCreationVisualRecordRes getVisualRecordByIdWithCheck(Long visualRecordId);
    
    /**
     * 检查访问权限
     * @param visualRecordId 视觉记录ID
     * @return 是否有权限访问
     */
    boolean checkAccessPermission(Long visualRecordId);

    /**
     * 触发视觉记录的渲染过程
     * @param req 包含 sessionId 的请求 DTO
     */
    void triggerRender(RenderTriggerReq req);

    /**
     * 更新指定 sessionId 记录的状态。
     * @param visualRecordCode 会话 ID
     * @param status 新的状态值
     */
    void updateStatus(String visualRecordCode, int status);

    /**
     * 更新指定 sessionId 记录的渲染进度。
     * @param visualRecordCode 会话 ID
     * @param progress 新的进度值 (0-100)
     */
    void updateProgress(String visualRecordCode, int progress);

    /**
     * 更新指定 sessionId 记录的视频 URL 和状态 (通常在成功完成时调用)。
     * @param visualRecordCode 会话 ID
     * @param url 渲染完成的视频 URL
     * @param status 新的状态值 (通常是成功状态)
     */
    void updateUrlAndStatus(String visualRecordCode, String url, int status);

    /**
     * 根据ID删除视觉记录 (逻辑删除)
     * @param visualRecordCode 记录ID
     */
    void deleteVisualRecordByCode(String visualRecordCode);

    /**
     * 根据视觉记录Code查询同会话下所有已发布的记录
     * @param visualRecordCode 视觉记录Code
     * @return 同会话下已发布的视觉记录列表
     */
    List<AiCreationVisualRecordListRes> getPublishedVisualRecordsByCode(String visualRecordCode);

    /**
     * 获取最新发布的10条记录
     * @return 最新发布的10条视觉记录列表
     */
    List<AiCreationVisualRecordListRes> getTop10PublishedRecords();

    /**
     * 分页查询已发布的视觉记录
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 分页结果，同一sessionId只返回按subtitle排序的第一条
     */
    PageResponse<AiCreationVisualRecordListRes> getPublishedRecordsByPage(Integer pageNum, Integer pageSize);

    /**
     * 根据会话ID查询会话所有参考图
     * @param sessionId 会话ID
     * @return 参考图列表
     */
    List<SessionReferenceImageRes> getSessionReferenceImages(String sessionId);
}