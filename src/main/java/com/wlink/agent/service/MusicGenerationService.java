package com.wlink.agent.service;

import com.wlink.agent.client.model.music.FalQueueStatus;
import com.wlink.agent.client.model.music.Lyria2Input;
import com.wlink.agent.client.model.music.Lyria2Output;
import com.wlink.agent.dao.po.AiMusicGenerationRecordPo;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 音乐生成服务接口
 * 提供音乐生成的业务逻辑封装
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-04
 */
public interface MusicGenerationService {

    /**
     * 同步生成音乐
     * 提交请求后等待完成并返回结果
     * 
     * @param input 音乐生成输入参数
     * @return 音乐生成结果
     * @throws IOException 如果生成失败
     */
    Lyria2Output generateMusic(Lyria2Input input) throws IOException;

    /**
     * 异步生成音乐
     * 提交请求后立即返回Future，可以异步获取结果
     * 
     * @param input 音乐生成输入参数
     * @return 音乐生成结果的Future
     */
    CompletableFuture<Lyria2Output> generateMusicAsync(Lyria2Input input);

    /**
     * 提交音乐生成请求
     * 只提交请求到队列，不等待完成
     * 
     * @param input 音乐生成输入参数
     * @return 队列状态信息
     * @throws IOException 如果提交失败
     */
    FalQueueStatus submitMusicGenerationRequest(Lyria2Input input) throws IOException;

    /**
     * 查询任务状态
     * 
     * @param requestId 请求ID
     * @return 队列状态信息
     * @throws IOException 如果查询失败
     */
    FalQueueStatus getTaskStatus(String requestId) throws IOException;

    /**
     * 获取任务结果
     * 
     * @param requestId 请求ID
     * @return 音乐生成结果
     * @throws IOException 如果获取失败
     */
    Lyria2Output getTaskResult(String requestId) throws IOException;

    /**
     * 取消任务
     * 
     * @param requestId 请求ID
     * @return 是否成功取消
     * @throws IOException 如果取消失败
     */
    boolean cancelTask(String requestId) throws IOException;

    /**
     * 等待任务完成
     * 
     * @param requestId 请求ID
     * @return 音乐生成结果
     * @throws IOException 如果等待失败或超时
     */
    Lyria2Output waitForTaskCompletion(String requestId) throws IOException;

    /**
     * 根据文本提示词生成音乐（简化方法）
     * 
     * @param prompt 音乐描述提示词
     * @return 音乐生成结果
     * @throws IOException 如果生成失败
     */
    Lyria2Output generateMusicByPrompt(String prompt) throws IOException;

    /**
     * 根据文本提示词异步生成音乐（简化方法）
     * 
     * @param prompt 音乐描述提示词
     * @return 音乐生成结果的Future
     */
    CompletableFuture<Lyria2Output> generateMusicByPromptAsync(String prompt);

    /**
     * 根据文本提示词和种子值生成音乐
     * 
     * @param prompt 音乐描述提示词
     * @param seed 种子值，用于确定性生成
     * @return 音乐生成结果
     * @throws IOException 如果生成失败
     */
    Lyria2Output generateMusicWithSeed(String prompt, Integer seed) throws IOException;

    /**
     * 根据完整参数生成音乐
     *
     * @param prompt 音乐描述提示词
     * @param seed 种子值
     * @param negativePrompt 负面提示词
     * @return 音乐生成结果
     * @throws IOException 如果生成失败
     */
    Lyria2Output generateMusicWithFullParams(String prompt, Integer seed, String negativePrompt) throws IOException;

    // ==================== 数据库查询方法 ====================

    /**
     * 根据请求ID查询生成记录
     *
     * @param requestId 请求ID
     * @return 音乐生成记录
     */
    AiMusicGenerationRecordPo getMusicGenerationRecord(String requestId);

    /**
     * 根据会话ID查询生成记录列表
     *
     * @param sessionId 会话ID
     * @return 音乐生成记录列表
     */
    List<AiMusicGenerationRecordPo> getMusicGenerationRecordsBySession(String sessionId);

    /**
     * 根据会话ID和章节ID查询生成记录列表
     *
     * @param sessionId 会话ID
     * @param segmentId 章节ID
     * @return 音乐生成记录列表
     */
    List<AiMusicGenerationRecordPo> getMusicGenerationRecordsBySessionAndSegment(String sessionId, String segmentId);

    /**
     * 根据用户ID查询生成记录列表
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 音乐生成记录列表
     */
    List<AiMusicGenerationRecordPo> getMusicGenerationRecordsByUser(String userId, Integer limit);

    /**
     * 查询用户最近的成功生成记录
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 成功生成记录列表
     */
    List<AiMusicGenerationRecordPo> getRecentSuccessRecords(String userId, Integer limit);

    /**
     * 统计用户的音乐生成数量
     *
     * @param userId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 生成数量
     */
    int countUserGenerations(String userId, String startTime, String endTime);

    /**
     * 查询正在处理中的任务数量
     *
     * @return 处理中的任务数量
     */
    int countProcessingTasks();

    /**
     * 查询用户正在处理中的任务数量
     *
     * @param userId 用户ID
     * @return 用户处理中的任务数量
     */
    int countUserProcessingTasks(String userId);
}
