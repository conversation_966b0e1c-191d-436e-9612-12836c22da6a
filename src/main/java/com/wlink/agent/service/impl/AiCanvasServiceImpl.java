package com.wlink.agent.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.exception.BizException;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.wlink.agent.dao.mapper.AiCanvasAudioMapper;
import com.wlink.agent.dao.mapper.AiCanvasImageMapper;
import com.wlink.agent.dao.mapper.AiCanvasMapper;
import com.wlink.agent.dao.mapper.AiCanvasMaterialMapper;
import com.wlink.agent.dao.mapper.AiCanvasShotMapper;
import com.wlink.agent.dao.mapper.AiCanvasTrackMapper;
import com.wlink.agent.dao.mapper.AiCanvasVideoMapper;
import com.wlink.agent.dao.mapper.AiCanvasBackgroundMusicMapper;
import com.wlink.agent.dao.mapper.AiSoundEffectsRecordMapper;
import com.wlink.agent.dao.mapper.AiTrackAudioMapper;
import com.wlink.agent.dao.mapper.AiChapterMapper;
import com.wlink.agent.dao.mapper.AiCreationSessionMapper;
import com.wlink.agent.dao.mapper.AiImageTaskQueueMapper;
import com.wlink.agent.dao.mapper.AiShotMapper;
import com.wlink.agent.dao.mapper.AiTtsRecordMapper;
import com.wlink.agent.dao.po.AiCanvasAudioPo;
import com.wlink.agent.dao.po.AiCanvasImagePo;
import com.wlink.agent.dao.po.AiCanvasMaterialPo;
import com.wlink.agent.dao.po.AiCanvasPo;
import com.wlink.agent.dao.po.AiCanvasShotPo;
import com.wlink.agent.dao.po.AiCanvasTrackPo;
import com.wlink.agent.dao.po.AiCanvasVideoPo;
import com.wlink.agent.dao.po.AiCanvasBackgroundMusicPo;
import com.wlink.agent.dao.po.AiSoundEffectsRecordPo;
import com.wlink.agent.dao.po.AiTrackAudioPo;
import com.wlink.agent.dao.po.AiChapterPo;
import com.wlink.agent.dao.po.AiCreationSessionPo;
import com.wlink.agent.dao.po.AiImageTaskQueuePo;
import com.wlink.agent.dao.po.AiShotPo;
import com.wlink.agent.dao.po.AiTtsRecordPo;
import com.wlink.agent.enums.TaskType;
import com.wlink.agent.model.dto.SimpleUserInfo;
import com.wlink.agent.model.req.CanvasUpdateReq;
import com.wlink.agent.model.req.ChapterToCanvasReq;
import com.wlink.agent.model.req.ShotOrderUpdateReq;
import com.wlink.agent.model.req.ShotSaveReq;
import com.wlink.agent.model.res.AiCanvasAudioRes;
import com.wlink.agent.model.res.AiCanvasDetailRes;
import com.wlink.agent.model.res.AiCanvasImageRes;
import com.wlink.agent.model.res.AiCanvasRes;
import com.wlink.agent.model.res.AiCanvasShotRes;
import com.wlink.agent.model.res.AiCanvasVideoRes;
import com.wlink.agent.model.res.CanvasBackgroundMusicRes;
import com.wlink.agent.service.AiCanvasAudioService;
import com.wlink.agent.service.AiCanvasService;
import com.wlink.agent.service.CanvasBackgroundMusicService;
import com.wlink.agent.utils.MediaUrlPrefixUtil;
import com.wlink.agent.utils.UserContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomUtils;
import org.checkerframework.checker.nullness.qual.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【ai_canvas(画布表)】的数据库操作Service实现
* @createDate 2025-06-23 17:39:35
*/
@Slf4j
@Service
@RequiredArgsConstructor
public class AiCanvasServiceImpl extends ServiceImpl<AiCanvasMapper, AiCanvasPo>
    implements AiCanvasService {

    private final AiCanvasShotMapper canvasShotMapper;
    private final AiCanvasImageMapper canvasImageMapper;
    private final AiCanvasVideoMapper canvasVideoMapper;
    private final AiCanvasAudioMapper canvasAudioMapper;
    private final AiCanvasMaterialMapper canvasMaterialMapper;
    private final AiCanvasTrackMapper canvasTrackMapper;
    private final AiTrackAudioMapper trackAudioMapper;
    private final AiCanvasBackgroundMusicMapper canvasBackgroundMusicMapper;
    private final AiChapterMapper aiChapterMapper;
    private final AiShotMapper aiShotMapper;
    private final AiImageTaskQueueMapper aiImageTaskQueueMapper;
    private final AiTtsRecordMapper aiTtsRecordMapper;
    private final AiCreationSessionMapper aiCreationSessionMapper;
    private final AiCanvasAudioService aiCanvasAudioService;
    private final AiSoundEffectsRecordMapper aiSoundEffectsRecordMapper;
    private final CanvasBackgroundMusicService canvasBackgroundMusicService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createCanvas() {
        // 查询用户已有的画布数量，用于生成默认名称
        SimpleUserInfo userInfo = UserContext.getUser();
        String userId = userInfo.getUserId();
        LambdaQueryWrapper<AiCanvasPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiCanvasPo::getUserId, userId)
                .eq(AiCanvasPo::getDelFlag, 0);
        long count = this.count(queryWrapper);

        // 生成默认名称，格式为"未命名x"
        String defaultName = "未命名" + (count + 1);
        // 创建新画布
        AiCanvasPo canvasPo = new AiCanvasPo();
        canvasPo.setUserId(userId);
        canvasPo.setCanvasName(defaultName);
        canvasPo.setCode(UUID.randomUUID().toString().replace("-", ""));
        canvasPo.setStatus(0); // 0-草稿
        canvasPo.setSortOrder(1);
        canvasPo.setSeed(RandomUtils.nextInt(10000, 99999));
        canvasPo.setCreateTime(new Date());
        canvasPo.setUpdateTime(new Date());
        canvasPo.setDelFlag(0);
        // 保存画布
        this.save(canvasPo);
        log.info("Created new canvas for user {}, id: {}, name: {}", userId, canvasPo.getId(), defaultName);
        return canvasPo.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCanvas(CanvasUpdateReq req) {
        if (req.getCanvasId() == null) {
            throw new BizException("画布ID不能为空");
        }

        // 查询画布是否存在
        AiCanvasPo canvasPo = this.getById(req.getCanvasId());
        if (canvasPo == null || canvasPo.getDelFlag() == 1) {
            log.info("Canvas not found, id: {}", req.getCanvasId());
            throw new BizException("画布不存在");
        }
        // 更新画布信息
        boolean needUpdate = false;

        if (StringUtils.hasText(req.getCanvasName())) {
            canvasPo.setCanvasName(req.getCanvasName());
            needUpdate = true;
        }

        if (StringUtils.hasText(req.getCoverImage())) {
            canvasPo.setCoverImage(req.getCoverImage().replace(MediaUrlPrefixUtil.MEDIA_URL_PREFIX, ""));
            needUpdate = true;
        }

        if (StringUtils.hasText(req.getRatio())) {
            canvasPo.setRatio(req.getRatio());
            needUpdate = true;
        }
        if (needUpdate) {
            canvasPo.setUpdateTime(new Date());
            this.updateById(canvasPo);
            log.info("Updated canvas, id: {}", req.getCanvasId());
        }

    }

    @Override
    public PageResponse<AiCanvasRes> getCanvasList(int pageNum, int pageSize) {
        String userId = UserContext.getUser().getUserId();
        // 构建分页查询
        Page<AiCanvasPo> page = new Page<>(pageNum, pageSize);

        // 构建查询条件
        LambdaQueryWrapper<AiCanvasPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiCanvasPo::getUserId, userId)
                .eq(AiCanvasPo::getDelFlag, 0)
                .orderByDesc(AiCanvasPo::getUpdateTime);

        // 执行查询
        IPage<AiCanvasPo> resultPage = this.page(page, queryWrapper);

        // 转换结果
        List<AiCanvasRes> resList = resultPage.getRecords().stream()
                .map(this::convertToCanvasRes)
                .collect(Collectors.toList());

        // 返回分页结果
        return PageResponse.of(resList, (int) resultPage.getTotal(), pageSize, pageNum);
    }

    @Override
    public AiCanvasDetailRes getCanvasDetail(Long canvasId) {
        // 查询画布信息
        AiCanvasPo canvasPo = this.getById(canvasId);
        if (canvasPo == null || canvasPo.getDelFlag() == 1) {
            log.error("Canvas not found, id: {}", canvasId);
            throw new BizException("画布不存在");
        }

        // 查询分镜列表
        LambdaQueryWrapper<AiCanvasShotPo> shotQueryWrapper = new LambdaQueryWrapper<>();
        shotQueryWrapper.eq(AiCanvasShotPo::getCanvasId, canvasId)
                .eq(AiCanvasShotPo::getDelFlag, 0)
                .orderByAsc(AiCanvasShotPo::getSortOrder);

        List<AiCanvasShotPo> shotList = canvasShotMapper.selectList(shotQueryWrapper);

        // 获取所有分镜编码
        List<String> shotCodes = shotList.stream()
                .map(AiCanvasShotPo::getCode)
                .collect(Collectors.toList());

        // 如果有分镜，查询相关资源
        Map<String, AiCanvasImagePo> imageMap = new HashMap<>();
        Map<String, AiCanvasVideoPo> videoMap = new HashMap<>();
        Map<String, List<AiCanvasAudioRes>> audioMap = new HashMap<>();

        if (!shotCodes.isEmpty()) {
            // 查询图片资源
            LambdaQueryWrapper<AiCanvasImagePo> imageQueryWrapper = new LambdaQueryWrapper<>();
            imageQueryWrapper.eq(AiCanvasImagePo::getCanvasId, canvasId)
                    .in(AiCanvasImagePo::getShotCode, shotCodes)
                    .eq(AiCanvasImagePo::getDelFlag, 0);

            List<AiCanvasImagePo> imageList = canvasImageMapper.selectList(imageQueryWrapper);

            // 按分镜编码分组，一个分镜只有一个图片，取第一个
            for (AiCanvasImagePo imagePo : imageList) {
                imageMap.putIfAbsent(imagePo.getShotCode(), imagePo);
            }

            // 查询视频资源
            LambdaQueryWrapper<AiCanvasVideoPo> videoQueryWrapper = new LambdaQueryWrapper<>();
            videoQueryWrapper.eq(AiCanvasVideoPo::getCanvasId, canvasId)
                    .in(AiCanvasVideoPo::getShotCode, shotCodes)
                    .eq(AiCanvasVideoPo::getDelFlag, 0);

            List<AiCanvasVideoPo> videoList = canvasVideoMapper.selectList(videoQueryWrapper);

            // 按分镜编码分组，一个分镜只有一个视频，取第一个
            for (AiCanvasVideoPo videoPo : videoList) {
                videoMap.putIfAbsent(videoPo.getShotCode(), videoPo);
            }

            // 查询音频资源
            LambdaQueryWrapper<AiCanvasAudioPo> audioQueryWrapper = new LambdaQueryWrapper<>();
            audioQueryWrapper.eq(AiCanvasAudioPo::getCanvasId, canvasId)
                    .in(AiCanvasAudioPo::getShotCode, shotCodes)
                    .eq(AiCanvasAudioPo::getDelFlag, 0)
                    .orderByAsc(AiCanvasAudioPo::getSortOrder);

            List<AiCanvasAudioPo> audioList = canvasAudioMapper.selectList(audioQueryWrapper);

            // 按分镜编码分组，音频可能有多个
            for (AiCanvasAudioPo audioPo : audioList) {
                AiCanvasAudioRes audioRes = convertToAudioRes(audioPo);
                audioMap.computeIfAbsent(audioPo.getShotCode(), k -> new ArrayList<>()).add(audioRes);
            }
        }

        // 转换结果
        AiCanvasDetailRes detailRes = new AiCanvasDetailRes();
        BeanUtils.copyProperties(canvasPo, detailRes);
        detailRes.setCoverImage(MediaUrlPrefixUtil.getMediaUrl(canvasPo.getCoverImage()));
        List<AiCanvasShotRes> shotResList = shotList.stream()
                .map(shotPo -> {
                    AiCanvasShotRes shotRes = convertToShotRes(shotPo);

                    // 设置资源
                    String shotCode = shotPo.getCode();

                    // 设置图片资源字段
                    AiCanvasImagePo imagePo = imageMap.get(shotCode);
                    if (imagePo != null) {
                        shotRes.setImageUrl(MediaUrlPrefixUtil.getMediaUrl(imagePo.getImageUrl()));
                        shotRes.setImagePrompt(imagePo.getImagePrompt());
                        shotRes.setImageDesc(imagePo.getImageDesc());
                        shotRes.setImageAspectRatio(imagePo.getImageAspectRatio());
                        shotRes.setImageStatus(imagePo.getImageStatus());
                        shotRes.setReferenceImage(MediaUrlPrefixUtil.getMediaUrl(imagePo.getReferenceImage()));
                        shotRes.setVideoConvertPrompt(imagePo.getVideoConvertPrompt());
                    }

                    // 设置视频资源字段
                    AiCanvasVideoPo videoPo = videoMap.get(shotCode);
                    if (videoPo != null) {
                        shotRes.setVideoUrl(MediaUrlPrefixUtil.getMediaUrl(videoPo.getVideoUrl()));
                        shotRes.setVideoPrompt(videoPo.getVideoPrompt());
                        shotRes.setVideoDesc(videoPo.getVideoDesc());
                        shotRes.setVideoDuration(videoPo.getVideoDuration());
                        shotRes.setVideoAspectRatio(videoPo.getVideoAspectRatio());
                        shotRes.setVideoStatus(videoPo.getVideoStatus());
                        shotRes.setStartFrameImage(MediaUrlPrefixUtil.getMediaUrl(videoPo.getStartFrameImage()));
                        shotRes.setEndFrameImage(MediaUrlPrefixUtil.getMediaUrl(videoPo.getEndFrameImage()));
                        shotRes.setVideoVolume(videoPo.getVolume()); // 设置视频音量
                    }

                    // 设置音频资源列表
                    shotRes.setAudios(audioMap.getOrDefault(shotCode, new ArrayList<>()));

                    return shotRes;
                }).sorted(Comparator.comparingInt(AiCanvasShotRes::getSortOrder))
                .collect(Collectors.toList());

        detailRes.setShots(shotResList);

        // 查询画布背景音乐
        CanvasBackgroundMusicRes backgroundMusic = canvasBackgroundMusicService.getCanvasBackgroundMusic(canvasId);
        detailRes.setBackgroundMusic(backgroundMusic);

        return detailRes;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateShotOrder(ShotOrderUpdateReq req) {
        if (req.getCanvasId() == null) {
            throw new BizException("画布ID不能为空");
        }

        if (req.getShotOrders() == null || req.getShotOrders().isEmpty()) {
            throw new BizException("分镜顺序列表不能为空");
        }

        // 验证画布是否存在且属于当前用户
        AiCanvasPo canvasPo = this.getById(req.getCanvasId());
        if (canvasPo == null || canvasPo.getDelFlag() == 1) {
            log.error("Canvas not found, id: {}", req.getCanvasId());
            throw new BizException("画布不存在");
        }

        String currentUserId = UserContext.getUser().getUserId();
        if (!currentUserId.equals(canvasPo.getUserId())) {
            log.error("User {} has no permission to update canvas {}", currentUserId, req.getCanvasId());
            throw new BizException("无权限操作此画布");
        }

        // 获取要更新的分镜ID列表
        List<Long> shotIds = req.getShotOrders().stream()
                .map(ShotOrderUpdateReq.ShotOrderItem::getId)
                .collect(Collectors.toList());

        // 验证分镜是否都属于该画布
        LambdaQueryWrapper<AiCanvasShotPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiCanvasShotPo::getCanvasId, req.getCanvasId())
                .in(AiCanvasShotPo::getId, shotIds)
                .eq(AiCanvasShotPo::getDelFlag, 0);

        List<AiCanvasShotPo> existingShots = canvasShotMapper.selectList(queryWrapper);

        if (existingShots.size() != shotIds.size()) {
            log.error("Some shots not found or not belong to canvas {}", req.getCanvasId());
            throw new BizException("部分分镜不存在或不属于该画布");
        }

        // 批量更新分镜顺序
        for (ShotOrderUpdateReq.ShotOrderItem orderItem : req.getShotOrders()) {
            AiCanvasShotPo shotPo = new AiCanvasShotPo();
            shotPo.setId(orderItem.getId());
            shotPo.setSortOrder(orderItem.getSortOrder());
            shotPo.setUpdateTime(new Date());
            canvasShotMapper.updateById(shotPo);
        }

        log.info("Updated shot order for canvas {}, updated {} shots", req.getCanvasId(), req.getShotOrders().size());
    }

    /**
     * 将画布PO转换为画布响应DTO
     */
    private AiCanvasRes convertToCanvasRes(AiCanvasPo canvasPo) {
        AiCanvasRes res = new AiCanvasRes();
        BeanUtils.copyProperties(canvasPo, res);
        res.setCoverImage(MediaUrlPrefixUtil.getMediaUrl(canvasPo.getCoverImage()));
        return res;
    }

    /**
     * 将分镜PO转换为分镜响应DTO
     */
    private AiCanvasShotRes convertToShotRes(AiCanvasShotPo shotPo) {
        AiCanvasShotRes res = new AiCanvasShotRes();
        BeanUtils.copyProperties(shotPo, res);
        return res;
    }

    /**
     * 将图片PO转换为图片响应DTO
     */
    private AiCanvasImageRes convertToImageRes(AiCanvasImagePo imagePo) {
        AiCanvasImageRes res = new AiCanvasImageRes();
        BeanUtils.copyProperties(imagePo, res);
        return res;
    }

    /**
     * 将视频PO转换为视频响应DTO
     */
    private AiCanvasVideoRes convertToVideoRes(AiCanvasVideoPo videoPo) {
        AiCanvasVideoRes res = new AiCanvasVideoRes();
        BeanUtils.copyProperties(videoPo, res);
        return res;
    }

    /**
     * 将音频PO转换为音频响应DTO
     */
    private AiCanvasAudioRes convertToAudioRes(AiCanvasAudioPo audioPo) {
        AiCanvasAudioRes res = new AiCanvasAudioRes();
        BeanUtils.copyProperties(audioPo, res);
        res.setAudioUrl(MediaUrlPrefixUtil.getMediaUrl(audioPo.getAudioUrl()));
        res.setAudioDuration(audioPo.getAudioDuration());
        return res;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long convertChapterToCanvas(ChapterToCanvasReq req) {
        String sessionId = req.getSessionId();
        String segmentId = req.getSegmentId();


        AiCreationSessionPo aiCreationSessionPo = aiCreationSessionMapper.selectOne(
                new LambdaQueryWrapper<AiCreationSessionPo>()
                        .eq(AiCreationSessionPo::getSessionId, sessionId)
                        .eq(AiCreationSessionPo::getDelFlag, 0)
        );
        if (aiCreationSessionPo == null) {
            log.error("会话不存在");
            throw new BizException("会话不存在");
        }


        // 1. 查询章节信息
        AiChapterPo chapterPo = aiChapterMapper.selectOne(
                new LambdaQueryWrapper<AiChapterPo>()
                        .eq(AiChapterPo::getSessionId, sessionId)
                        .eq(AiChapterPo::getSegmentId, segmentId)
                        .eq(AiChapterPo::getDelFlag, 0)
        );

        if (chapterPo == null) {
            throw new BizException("章节不存在");
        }

        // 2. 查询分镜信息
        List<AiShotPo> shotList = aiShotMapper.selectList(
                new LambdaQueryWrapper<AiShotPo>()
                        .eq(AiShotPo::getSessionId, sessionId)
                        .eq(AiShotPo::getSegmentId, segmentId)
                        .eq(AiShotPo::getDelFlag, 0)
                        .orderByAsc(AiShotPo::getQueue)
                
        );

        if (shotList.isEmpty()) {
            throw new BizException("章节下没有分镜数据");
        }




        // 3. 检查画布是否已存在
        String userId = UserContext.getUser().getUserId();
        LambdaQueryWrapper<AiCanvasPo> canvasQueryWrapper = new LambdaQueryWrapper<>();
        canvasQueryWrapper.eq(AiCanvasPo::getSessionId, sessionId)
                .eq(AiCanvasPo::getSegmentId, segmentId)
                .eq(AiCanvasPo::getUserId, userId)
                .eq(AiCanvasPo::getDelFlag, 0)
                .orderByDesc(AiCanvasPo::getUpdateTime)
                .last("limit 1");

        AiCanvasPo existingCanvas = this.getOne(canvasQueryWrapper);
        Long canvasId;
        existingCanvas= null;
        if (existingCanvas != null) {
            // 画布已存在，覆盖数据
            log.info("Canvas already exists, will override data: canvasId={}, sessionId={}, segmentId={}",
                    existingCanvas.getId(), sessionId, segmentId);

            canvasId = existingCanvas.getId();

            // 更新画布基本信息
            existingCanvas.setCanvasName(chapterPo.getSegmentName());
            existingCanvas.setUpdateTime(new Date());
            this.updateById(existingCanvas);

            // 删除现有的分镜数据
            clearExistingCanvasData(canvasId);

        } else {
            // 画布不存在，创建新画布
            log.info("Canvas does not exist, creating new canvas: sessionId={}, segmentId={}", sessionId, segmentId);

            AiCanvasPo canvasPo = new AiCanvasPo();
            canvasPo.setUserId(userId);
            canvasPo.setSessionId(sessionId);
            canvasPo.setSegmentId(chapterPo.getSegmentId());
            canvasPo.setCanvasName(chapterPo.getSegmentName());
            canvasPo.setRatio(aiCreationSessionPo.getImageSize());
            canvasPo.setCode(UUID.randomUUID().toString().replace("-", ""));
            canvasPo.setStatus(0); // 0-草稿
            canvasPo.setSortOrder(1);
            canvasPo.setSeed(Integer.parseInt(req.getSessionId().substring(req.getSessionId().length() - 8)));
            canvasPo.setCreateTime(new Date());
            canvasPo.setUpdateTime(new Date());
            canvasPo.setDelFlag(0);

            // 保存画布
            this.save(canvasPo);
            canvasId = canvasPo.getId();
        }

        // 4. 处理每个分镜
        int sortOrder = 1;
        for (AiShotPo shotPo : shotList) {
            // 解析分镜数据
            ShotSaveReq.ShotGroupsDTO.ShotsDTO shotData = JSON.parseObject(
                    shotPo.getShotData(),
                    ShotSaveReq.ShotGroupsDTO.ShotsDTO.class
            );

            if (shotData == null) {
                log.warn("Shot data is null for shot: {}", shotPo.getId());
                continue;
            }

            // 创建画布分镜
            AiCanvasShotPo canvasShotPo = new AiCanvasShotPo();
            canvasShotPo.setCanvasId(canvasId);
            canvasShotPo.setCode(generateShotCode()); // 使用场景ID-分镜ID作为编码
            canvasShotPo.setOriginalShotId(shotPo.getId());
            canvasShotPo.setType("image");
            canvasShotPo.setComposition(shotData.getComposition());
            canvasShotPo.setMovement(shotData.getMovement());
            canvasShotPo.setSortOrder(sortOrder++);
            canvasShotPo.setShotStatus(2); // 0-初始
            canvasShotPo.setCreateTime(new Date());
            canvasShotPo.setUpdateTime(new Date());
            canvasShotPo.setDelFlag(0);

            // 保存画布分镜
            canvasShotMapper.insert(canvasShotPo);
            String shotCode = canvasShotPo.getCode();

            // 查询图片状态
            AiImageTaskQueuePo aiImageTaskQueuePo = aiImageTaskQueueMapper.selectOne(
                    new LambdaQueryWrapper<AiImageTaskQueuePo>()
                            .eq(AiImageTaskQueuePo::getSessionId, shotPo.getSessionId())
                            .eq(AiImageTaskQueuePo::getContentId, shotPo.getShotId())
                            .eq(AiImageTaskQueuePo::getContentType, 4)
                            .eq(AiImageTaskQueuePo::getTaskType, TaskType.GENERATE.getValue())
                            .orderByDesc(AiImageTaskQueuePo::getCreateTime)
                            .last("limit 1")
            );
            // 5. 处理图片资源
            if (Objects.nonNull(aiImageTaskQueuePo)) {
                String requestParams = aiImageTaskQueuePo.getRequestParams();
                JSONObject requestParamsJson = JSONObject.parseObject(requestParams);
                String prompt = "";
                String aspectRatio= "";
                if (Objects.equals(aiImageTaskQueuePo.getImageModel(),"FLUX")){
                     aspectRatio = requestParamsJson.getString("aspect_ratio");

                }else {
                    /**
                     * {"model":"doubao-seedream-3-0-t2i-250415","prompt":"二次元动漫风格风格，### 1. 镜头设定\n- 镜头类型：中景→近景→特写\n- 拍摄角度：平视\n- 镜头运动：静止→缓慢推进\n- 景深效果：浅景深（聚焦吕树面部和胸口）\n- 画面比例：16:9\n\n### 2. 人物刻画\n**主要人物：**\n- 角色描述：十七岁少年，体型瘦削，脸色苍白。穿着朴素的冬季常服，脖子上戴着黑核桃吊坠。\n- 面部表情：眉宇紧锁，瞳孔放大，眼神充满震惊和疑惑。嘴唇微张，呼吸急促。\n- 动作状态：双手微微握拳，身体轻微颤抖，胸口剧烈起伏。\n- 画面位置：中景位于画面中央，近景和特写聚焦面部和胸口。\n\n**次要人物：**\n- 吕小鱼：站在吕树身旁，仰头看着火球，表情惊讶但无恐惧，手中拿着零食。\n- 黑衣人：站在观众席后方，冷峻地观察着舞台和人群。\n\n### 3. 场景描述\n- 时代背景：现代都市，冬季夜晚。\n- 地理环境：室内表演场地，观众席灯光昏暗，舞台灯光绚烂。\n- 空间布局：前景为吕树和吕小鱼，中景为其他观众，背景为舞台和火焰表演者。\n- 光照条件：火球发出的暖色强光投射在吕树脸上，周围环境为冷色调的暗光。\n- 色彩基调：主色调为红橙色的火焰光，辅以冷蓝的阴影。\n- 重要道具：火球在画面中占据重要位置，光芒四射，照亮吕树的脸和胸口。\n\n### 4. 绘画风格\n- 线条特征：中等粗细，流畅且有张力，强调光影对比。\n- 色彩处理：高饱和度火焰光，低饱和度环境色，强对比突出焦点。\n- 细节精度：面部和火焰高精度，背景适度简化。\n\n### 5. 视觉焦点\n- 焦点区域：吕树的面部和胸口。\n- 视觉元素：火球的光芒在吕树眼中和胸口的反射。\n- 关键细节：吕树的表情和胸口剧烈起伏的细节。\n- 情绪氛围：紧张、震惊和神秘的共鸣感。","response_format":"url","size":"2048x1152","seed":95121920,"guidance_scale":7.5,"watermark":false}
                     */
                    String size = requestParamsJson.getString("size");
                    String[] sizeArray = size.split("x");
                    String width = sizeArray[0];
                    String height = sizeArray[1];
                    aspectRatio = calculateAspectRatio(Integer.parseInt(width), Integer.parseInt(height));
                }
                AiCanvasImagePo canvasImagePo = new AiCanvasImagePo();
                canvasImagePo.setCanvasId(canvasId);
                canvasImagePo.setShotCode(shotCode);
                canvasImagePo.setImageUrl(aiImageTaskQueuePo.getImageResult());
                canvasImagePo.setImageDesc(shotData.getComposition());
                canvasImagePo.setReferenceImage(aiImageTaskQueuePo.getImageResult());
                canvasImagePo.setVideoConvertPrompt(shotData.getImageInfo());
                canvasImagePo.setImageAspectRatio(aspectRatio); // 默认宽高比
                canvasImagePo.setImageStatus("COMPLETED"); // 已完成状态
                canvasImagePo.setCreateTime(new Date());
                canvasImagePo.setUpdateTime(new Date());
                canvasImagePo.setDelFlag(0);
                // 保存画布图片
                canvasImageMapper.insert(canvasImagePo);

                //增加到素材表
                AiCanvasMaterialPo aiCanvasMaterialPo = new AiCanvasMaterialPo();
                aiCanvasMaterialPo.setCanvasId(canvasId);
                aiCanvasMaterialPo.setMaterialType(1);
                aiCanvasMaterialPo.setMaterialSource(1);
                aiCanvasMaterialPo.setGenerationRecordId(aiImageTaskQueuePo.getId());
                aiCanvasMaterialPo.setMaterialUrl(aiImageTaskQueuePo.getImageResult());
                aiCanvasMaterialPo.setCreateTime(new Date());
                aiCanvasMaterialPo.setUpdateTime(new Date());
                aiCanvasMaterialPo.setDelFlag(0);
                canvasMaterialMapper.insert(aiCanvasMaterialPo);

            }
            // 查询语音信息
            List<AiTtsRecordPo> allTtsRecords = aiTtsRecordMapper.selectList(
                    new LambdaQueryWrapper<AiTtsRecordPo>()
                            .eq(AiTtsRecordPo::getConversationId, shotPo.getSessionId())
                            .eq(AiTtsRecordPo::getContentId, shotPo.getShotId())
                            .eq(AiTtsRecordPo::getStatus,1)
                            .orderByDesc(AiTtsRecordPo::getCreateTime));

            // 根据audio_index去重，同样的index只保留最近的一条
            List<AiTtsRecordPo> aiTtsRecordPos = allTtsRecords.stream()
                    .collect(Collectors.groupingBy(AiTtsRecordPo::getAudioIndex))
                    .values()
                    .stream()
                    .map(records -> records.stream()
                            .max(Comparator.comparing(AiTtsRecordPo::getCreateTime))
                            .orElse(null))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            List<AiSoundEffectsRecordPo> aiSoundEffectsRecordPos = aiSoundEffectsRecordMapper.selectList(new LambdaQueryWrapper<AiSoundEffectsRecordPo>()
                    .eq(AiSoundEffectsRecordPo::getSessionId, shotPo.getSessionId())
                    .eq(AiSoundEffectsRecordPo::getContentId, shotPo.getShotId())
                    .eq(AiSoundEffectsRecordPo::getGenerationStatus, "SUCCESS")
                    .orderByDesc(AiSoundEffectsRecordPo::getCreateTime));

            Map<Integer, AiSoundEffectsRecordPo> effectsRecordPoMap;
            if (CollUtil.isNotEmpty(aiSoundEffectsRecordPos)) {
                effectsRecordPoMap = aiSoundEffectsRecordPos.stream().collect(Collectors.toMap(AiSoundEffectsRecordPo::getAudioIndex, Function.identity(), (existing, replacement) -> existing));
            } else {
                effectsRecordPoMap = null;
            }
            Map<Integer, AiTtsRecordPo> ttsRecordMap;
            if (CollUtil.isNotEmpty(aiTtsRecordPos)) {
                // 创建一个Map，以index为key，方便快速查找
                ttsRecordMap = aiTtsRecordPos.stream()
                        .collect(Collectors.toMap(AiTtsRecordPo::getAudioIndex, Function.identity(), (existing, replacement) -> existing));
            } else {
                ttsRecordMap = null;
            }
            // 6. 处理音频资源 - 旁白
            List<AiCanvasAudioPo> audioPos = Lists.newArrayList();
            for (ShotSaveReq.ShotLines shotLine : shotData.getLineList()) {
                if (CollUtil.isNotEmpty(ttsRecordMap)){
                    AiTtsRecordPo aiTtsRecordPo = ttsRecordMap.get(shotLine.getId());
                    if (aiTtsRecordPo != null && !Objects.equals(shotLine.getName(),"音效")) {
                        AiCanvasAudioPo canvasAudioPo = new AiCanvasAudioPo();
                        canvasAudioPo.setCanvasId(canvasId);
                        canvasAudioPo.setShotCode(shotCode);
                        canvasAudioPo.setAudioType(1); // 1-旁白
                        canvasAudioPo.setText(aiTtsRecordPo.getText());
                        canvasAudioPo.setVoiceId(String.valueOf(aiTtsRecordPo.getVoiceId()));
                        canvasAudioPo.setAudioDuration(aiTtsRecordPo.getAudioLength());
                        canvasAudioPo.setSortOrder(null == aiTtsRecordPo.getAudioIndex() ? 1 : aiTtsRecordPo.getAudioIndex());
                        canvasAudioPo.setAudioUrl(aiTtsRecordPo.getAudioUrl());
                        canvasAudioPo.setTtsrecordId(aiTtsRecordPo.getId());
                        canvasAudioPo.setVolume(new java.math.BigDecimal("1.00")); // 默认音量为1.00
                        canvasAudioPo.setCreateTime(new Date());
                        canvasAudioPo.setUpdateTime(new Date());
                        canvasAudioPo.setDelFlag(0);
                        audioPos.add(canvasAudioPo);
                    }
                }
                if (CollUtil.isNotEmpty(effectsRecordPoMap) && Objects.equals(shotLine.getName(),"音效")){
                    AiSoundEffectsRecordPo aiSoundEffectsRecordPo = effectsRecordPoMap.get(shotLine.getId());
                    if (aiSoundEffectsRecordPo != null) {
                        AiCanvasAudioPo canvasAudioPo = new AiCanvasAudioPo();
                        canvasAudioPo.setCanvasId(canvasId);
                        canvasAudioPo.setShotCode(shotCode);
                        canvasAudioPo.setAudioType(2); // 1-旁白
                        canvasAudioPo.setText("");
                        canvasAudioPo.setVoiceId(String.valueOf(0));
                        canvasAudioPo.setAudioDuration(aiSoundEffectsRecordPo.getDuration() * 1000L);
                        canvasAudioPo.setSortOrder(aiSoundEffectsRecordPo.getAudioIndex());
                        canvasAudioPo.setAudioUrl(aiSoundEffectsRecordPo.getOssAudioUrl());
                        canvasAudioPo.setCreateTime(new Date());
                        canvasAudioPo.setUpdateTime(new Date());
                        canvasAudioPo.setDelFlag(0);
                        audioPos.add(canvasAudioPo);
                    }
                 }
            }
            if (CollUtil.isNotEmpty(audioPos)){
                // 保存画布音频
                aiCanvasAudioService.saveBatch(audioPos);
            }
        }

        log.info("Successfully converted chapter to canvas: chapterId={}, canvasId={}", chapterPo.getId(), canvasId);
        return canvasId;
    }
    /**
     * 生成分镜编码
     */
    private String generateShotCode() {
        return "SHOT-" + UUID.randomUUID().toString().replace("-", "").substring(0, 8).toUpperCase();
    }


    /**
     * 计算宽高比，格式为"9:16"
     *
     * @param width 宽度
     * @param height 高度
     * @return 格式化的宽高比字符串
     */
    private String calculateAspectRatio(int width, int height) {
        if (width <= 0 || height <= 0) {
            return "1:1"; // 默认正方形
        }

        // 计算最大公约数
        int gcd = gcd(width, height);

        // 使用最大公约数计算最简比例
        int aspectWidth = width / gcd;
        int aspectHeight = height / gcd;
        if (aspectHeight == 3 || aspectHeight == 7){
            aspectWidth = 3 * aspectWidth;
            aspectHeight = 3 * aspectHeight;
        }
        return aspectWidth + ":" + aspectHeight;
    }

    /**
     * 计算最大公约数的辅助方法
     */
    private int gcd(int a, int b) {
        return b == 0 ? a : gcd(b, a % b);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteCanvas(Long canvasId) {
        if (canvasId == null) {
            throw new BizException("画布ID不能为空");
        }

        // 查询画布是否存在
        AiCanvasPo canvasPo = this.getById(canvasId);
        if (canvasPo == null || canvasPo.getDelFlag() == 1) {
            log.error("Canvas not found or already deleted, id: {}", canvasId);
            throw new BizException("画布不存在或已被删除");
        }

        // 验证用户权限
        String currentUserId = UserContext.getUser().getUserId();
        if (!currentUserId.equals(canvasPo.getUserId())) {
            log.error("User {} has no permission to delete canvas {}", currentUserId, canvasId);
            throw new BizException("无权限删除此画布");
        }
        // 执行逻辑删除画布
        this.removeById(canvasPo.getId());

        // 删除画布下的所有分镜
        LambdaUpdateWrapper<AiCanvasShotPo> shotUpdateWrapper = new LambdaUpdateWrapper<>();
        shotUpdateWrapper.eq(AiCanvasShotPo::getCanvasId, canvasId)
                .eq(AiCanvasShotPo::getDelFlag, 0)
                .set(AiCanvasShotPo::getDelFlag, 1)
                .set(AiCanvasShotPo::getUpdateTime, new Date());
        canvasShotMapper.update(null, shotUpdateWrapper);

        // 删除画布下的所有图片资源
        LambdaUpdateWrapper<AiCanvasImagePo> imageUpdateWrapper = new LambdaUpdateWrapper<>();
        imageUpdateWrapper.eq(AiCanvasImagePo::getCanvasId, canvasId)
                .eq(AiCanvasImagePo::getDelFlag, 0)
                .set(AiCanvasImagePo::getDelFlag, 1)
                .set(AiCanvasImagePo::getUpdateTime, new Date());
        canvasImageMapper.update(null, imageUpdateWrapper);

        // 删除画布下的所有视频资源
        LambdaUpdateWrapper<AiCanvasVideoPo> videoUpdateWrapper = new LambdaUpdateWrapper<>();
        videoUpdateWrapper.eq(AiCanvasVideoPo::getCanvasId, canvasId)
                .eq(AiCanvasVideoPo::getDelFlag, 0)
                .set(AiCanvasVideoPo::getDelFlag, 1)
                .set(AiCanvasVideoPo::getUpdateTime, new Date());
        canvasVideoMapper.update(null, videoUpdateWrapper);

        // 删除画布下的所有音频资源
        LambdaUpdateWrapper<AiCanvasAudioPo> audioUpdateWrapper = new LambdaUpdateWrapper<>();
        audioUpdateWrapper.eq(AiCanvasAudioPo::getCanvasId, canvasId)
                .eq(AiCanvasAudioPo::getDelFlag, 0)
                .set(AiCanvasAudioPo::getDelFlag, 1)
                .set(AiCanvasAudioPo::getUpdateTime, new Date());
        canvasAudioMapper.update(null, audioUpdateWrapper);

        // 删除画布下的背景音乐
        clearCanvasBackgroundMusic(canvasId);

        log.info("Deleted canvas and all related resources: canvasId={}, user={}", canvasId, currentUserId);
    }

    @Override
    public AiCanvasDetailRes getCanvasBySessionAndSegment(String sessionId, String segmentId) {
        if (!StringUtils.hasText(sessionId)) {
            throw new BizException("会话ID不能为空");
        }
        if (!StringUtils.hasText(segmentId)) {
            throw new BizException("章节ID不能为空");
        }

        // 获取当前用户ID
        String currentUserId = UserContext.getUser().getUserId();

        // 根据sessionId和segmentId查询画布
        LambdaQueryWrapper<AiCanvasPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiCanvasPo::getSessionId, sessionId)
                .eq(AiCanvasPo::getSegmentId, segmentId)
                .eq(AiCanvasPo::getUserId, currentUserId)
                .eq(AiCanvasPo::getDelFlag, 0)
                .orderByDesc(AiCanvasPo::getUpdateTime)
                .last("limit 1"); // 取最新的一条记录

        AiCanvasPo canvasPo = this.getOne(queryWrapper);
        if (canvasPo == null) {
            log.info("Canvas not found for sessionId: {}, segmentId: {}, userId: {}",
                    sessionId, segmentId, currentUserId);
            return null;
        }
        // 复用现有的getCanvasDetail方法获取完整的画布详情
        AiCanvasDetailRes aiCanvasDetailRes = new AiCanvasDetailRes();
        aiCanvasDetailRes.setId(canvasPo.getId());
        aiCanvasDetailRes.setCanvasName(canvasPo.getCanvasName());
        return aiCanvasDetailRes;
    }

    /**
     * 清理现有画布的所有数据（分镜、图片、音频、素材）
     *
     * @param canvasId 画布ID
     */
    private void clearExistingCanvasData(Long canvasId) {
        log.info("Clearing existing canvas data: canvasId={}", canvasId);

        // 删除画布下的所有分镜
        LambdaUpdateWrapper<AiCanvasShotPo> shotUpdateWrapper = new LambdaUpdateWrapper<>();
        shotUpdateWrapper.eq(AiCanvasShotPo::getCanvasId, canvasId)
                .eq(AiCanvasShotPo::getDelFlag, 0)
                .set(AiCanvasShotPo::getDelFlag, 1)
                .set(AiCanvasShotPo::getUpdateTime, new Date());
        canvasShotMapper.update(null, shotUpdateWrapper);

        // 删除画布下的所有图片资源
        LambdaUpdateWrapper<AiCanvasImagePo> imageUpdateWrapper = new LambdaUpdateWrapper<>();
        imageUpdateWrapper.eq(AiCanvasImagePo::getCanvasId, canvasId)
                .eq(AiCanvasImagePo::getDelFlag, 0)
                .set(AiCanvasImagePo::getDelFlag, 1)
                .set(AiCanvasImagePo::getUpdateTime, new Date());
        canvasImageMapper.update(null, imageUpdateWrapper);

        // 删除画布下的所有音频资源
        LambdaUpdateWrapper<AiCanvasAudioPo> audioUpdateWrapper = new LambdaUpdateWrapper<>();
        audioUpdateWrapper.eq(AiCanvasAudioPo::getCanvasId, canvasId)
                .eq(AiCanvasAudioPo::getDelFlag, 0)
                .set(AiCanvasAudioPo::getDelFlag, 1)
                .set(AiCanvasAudioPo::getUpdateTime, new Date());
        canvasAudioMapper.update(null, audioUpdateWrapper);

        // 删除画布下的所有素材资源
        LambdaUpdateWrapper<AiCanvasMaterialPo> materialUpdateWrapper = new LambdaUpdateWrapper<>();
        materialUpdateWrapper.eq(AiCanvasMaterialPo::getCanvasId, canvasId)
                .eq(AiCanvasMaterialPo::getDelFlag, 0)
                .set(AiCanvasMaterialPo::getDelFlag, 1)
                .set(AiCanvasMaterialPo::getUpdateTime, new Date());
        canvasMaterialMapper.update(null, materialUpdateWrapper);

        // 删除画布下的所有音轨数据
        clearCanvasTrackData(canvasId);

        // 删除画布下的背景音乐
        clearCanvasBackgroundMusic(canvasId);

        log.info("Cleared existing canvas data: canvasId={}", canvasId);
    }

    /**
     * 清理画布下的音轨数据
     *
     * @param canvasId 画布ID
     */
    private void clearCanvasTrackData(Long canvasId) {
        log.info("Clearing canvas track data: canvasId={}", canvasId);

        // 1. 获取画布下所有音轨ID
        List<AiCanvasTrackPo> tracks = canvasTrackMapper.selectByCanvasId(canvasId);
        if (tracks.isEmpty()) {
            log.info("No tracks found for canvas: canvasId={}", canvasId);
            return;
        }

        List<Long> trackIds = tracks.stream()
                .map(AiCanvasTrackPo::getId)
                .collect(Collectors.toList());

        // 2. 删除所有音轨下的音频
        LambdaUpdateWrapper<AiTrackAudioPo> audioUpdateWrapper = new LambdaUpdateWrapper<>();
        audioUpdateWrapper.in(AiTrackAudioPo::getTrackId, trackIds)
                .eq(AiTrackAudioPo::getDelFlag, 0)
                .set(AiTrackAudioPo::getDelFlag, 1)
                .set(AiTrackAudioPo::getUpdateTime, new Date());
        trackAudioMapper.update(null, audioUpdateWrapper);

        // 3. 删除所有音轨
        LambdaUpdateWrapper<AiCanvasTrackPo> trackUpdateWrapper = new LambdaUpdateWrapper<>();
        trackUpdateWrapper.eq(AiCanvasTrackPo::getCanvasId, canvasId)
                .eq(AiCanvasTrackPo::getDelFlag, 0)
                .set(AiCanvasTrackPo::getDelFlag, 1)
                .set(AiCanvasTrackPo::getUpdateTime, new Date());
        canvasTrackMapper.update(null, trackUpdateWrapper);

        log.info("Cleared canvas track data: canvasId={}, trackCount={}", canvasId, tracks.size());
    }

    /**
     * 清理画布下的背景音乐数据
     *
     * @param canvasId 画布ID
     */
    private void clearCanvasBackgroundMusic(Long canvasId) {
        log.info("Clearing canvas background music: canvasId={}", canvasId);

        // 删除画布背景音乐
        LambdaUpdateWrapper<AiCanvasBackgroundMusicPo> musicUpdateWrapper = new LambdaUpdateWrapper<>();
        musicUpdateWrapper.eq(AiCanvasBackgroundMusicPo::getCanvasId, canvasId)
                .eq(AiCanvasBackgroundMusicPo::getDelFlag, 0)
                .set(AiCanvasBackgroundMusicPo::getDelFlag, 1)
                .set(AiCanvasBackgroundMusicPo::getUpdateTime, new Date());
        canvasBackgroundMusicMapper.update(null, musicUpdateWrapper);

        log.info("Cleared canvas background music: canvasId={}", canvasId);
    }
}



