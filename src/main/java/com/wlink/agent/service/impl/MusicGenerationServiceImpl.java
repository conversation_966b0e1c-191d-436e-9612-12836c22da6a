package com.wlink.agent.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wlink.agent.client.FalAiLyria2Client;
import com.wlink.agent.client.model.music.FalQueueStatus;
import com.wlink.agent.client.model.music.Lyria2Input;
import com.wlink.agent.client.model.music.Lyria2Output;
import com.wlink.agent.dao.mapper.AiMusicGenerationRecordMapper;
import com.wlink.agent.dao.po.AiMusicGenerationRecordPo;
import com.wlink.agent.service.MusicGenerationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 音乐生成服务实现
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-04
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MusicGenerationServiceImpl implements MusicGenerationService {

    private final FalAiLyria2Client falAiLyria2Client;
    private final AiMusicGenerationRecordMapper musicGenerationRecordMapper;
    private final ObjectMapper objectMapper;

    @Override
    public Lyria2Output generateMusic(Lyria2Input input) throws IOException {
        log.info("开始生成音乐: prompt={}", input.getPrompt());
        
        try {
            // 验证输入参数
            input.validate();
            
            // 同步生成音乐
            Lyria2Output result = falAiLyria2Client.generateMusicSync(input);
            
            log.info("音乐生成完成: prompt={}, audioUrl={}, summary={}", 
                    input.getPrompt(), result.getAudioUrl(), result.getSummary());
            
            return result;
            
        } catch (Exception e) {
            log.error("音乐生成失败: prompt={}, error={}", input.getPrompt(), e.getMessage(), e);
            throw e;
        }
    }

    @Override
    @Async("taskExecutor")
    public CompletableFuture<Lyria2Output> generateMusicAsync(Lyria2Input input) {
        log.info("开始异步生成音乐: prompt={}", input.getPrompt());
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                return generateMusic(input);
            } catch (IOException e) {
                log.error("异步音乐生成失败: prompt={}, error={}", input.getPrompt(), e.getMessage(), e);
                throw new RuntimeException("异步音乐生成失败", e);
            }
        });
    }

    @Override
    public FalQueueStatus submitMusicGenerationRequest(Lyria2Input input) throws IOException {
        return submitMusicGenerationRequest(input, null, null, null);
    }

    /**
     * 提交音乐生成请求（带会话信息）
     *
     * @param input 音乐生成输入参数
     * @param sessionId 会话ID
     * @param segmentId 章节ID
     * @param userId 用户ID
     * @return 队列状态信息
     * @throws IOException 如果提交失败
     */
    public FalQueueStatus submitMusicGenerationRequest(Lyria2Input input, String sessionId, String segmentId, String userId) throws IOException {
        log.info("提交音乐生成请求: prompt={}, sessionId={}, segmentId={}, userId={}",
                input.getPrompt(), sessionId, segmentId, userId);

        AiMusicGenerationRecordPo record = null;

        try {
            // 验证输入参数
            input.validate();

            // 创建数据库记录
            record = createMusicGenerationRecord(input, sessionId, segmentId, userId);

            // 提交请求
            FalQueueStatus status = falAiLyria2Client.submitMusicGenerationRequestWithRetry(input);

            // 更新记录的请求ID和状态
            updateRecordWithQueueStatus(record, status);

            log.info("音乐生成请求已提交: prompt={}, requestId={}, status={}",
                    input.getPrompt(), status.getRequestId(), status.getStatus());

            return status;

        } catch (Exception e) {
            log.error("提交音乐生成请求失败: prompt={}, error={}", input.getPrompt(), e.getMessage(), e);

            // 更新记录状态为失败
            if (record != null) {
                updateRecordStatus(record.getId(), AiMusicGenerationRecordPo.Status.FAILED, e.getMessage());
            }

            throw e;
        }
    }

    @Override
    public FalQueueStatus getTaskStatus(String requestId) throws IOException {
        log.debug("查询任务状态: requestId={}", requestId);
        
        try {
            FalQueueStatus status = falAiLyria2Client.getTaskStatus(requestId);
            
            log.debug("任务状态查询完成: requestId={}, status={}, description={}", 
                     requestId, status.getStatus(), status.getStatusDescription());
            
            return status;
            
        } catch (Exception e) {
            log.error("查询任务状态失败: requestId={}, error={}", requestId, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Lyria2Output getTaskResult(String requestId) throws IOException {
        log.info("获取任务结果: requestId={}", requestId);

        try {
            Lyria2Output result = falAiLyria2Client.getTaskResultWithRetry(requestId);

            // 更新数据库记录
            updateRecordWithResult(requestId, result);

            log.info("任务结果获取完成: requestId={}, audioUrl={}, summary={}",
                    requestId, result.getAudioUrl(), result.getSummary());

            return result;

        } catch (Exception e) {
            log.error("获取任务结果失败: requestId={}, error={}", requestId, e.getMessage(), e);

            // 更新记录状态为失败
            AiMusicGenerationRecordPo record = musicGenerationRecordMapper.selectByRequestId(requestId);
            if (record != null) {
                updateRecordStatus(record.getId(), AiMusicGenerationRecordPo.Status.FAILED, e.getMessage());
            }

            throw e;
        }
    }

    @Override
    public boolean cancelTask(String requestId) throws IOException {
        log.info("取消任务: requestId={}", requestId);
        
        try {
            boolean cancelled = falAiLyria2Client.cancelTask(requestId);
            
            if (cancelled) {
                log.info("任务取消成功: requestId={}", requestId);
            } else {
                log.warn("任务取消失败: requestId={}", requestId);
            }
            
            return cancelled;
            
        } catch (Exception e) {
            log.error("取消任务失败: requestId={}, error={}", requestId, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Lyria2Output waitForTaskCompletion(String requestId) throws IOException {
        log.info("等待任务完成: requestId={}", requestId);

        try {
            Lyria2Output result = falAiLyria2Client.waitForCompletion(requestId);

            // 更新数据库记录
            updateRecordWithResult(requestId, result);

            log.info("任务等待完成: requestId={}, audioUrl={}, summary={}",
                    requestId, result.getAudioUrl(), result.getSummary());

            return result;

        } catch (Exception e) {
            log.error("等待任务完成失败: requestId={}, error={}", requestId, e.getMessage(), e);

            // 更新记录状态为失败
            AiMusicGenerationRecordPo record = musicGenerationRecordMapper.selectByRequestId(requestId);
            if (record != null) {
                updateRecordStatus(record.getId(), AiMusicGenerationRecordPo.Status.FAILED, e.getMessage());
            }

            throw e;
        }
    }

    @Override
    public Lyria2Output generateMusicByPrompt(String prompt) throws IOException {
        if (prompt == null || prompt.trim().isEmpty()) {
            throw new IllegalArgumentException("提示词不能为空");
        }
        
        Lyria2Input input = Lyria2Input.simple(prompt);
        return generateMusic(input);
    }

    @Override
    @Async("taskExecutor")
    public CompletableFuture<Lyria2Output> generateMusicByPromptAsync(String prompt) {
        log.info("开始异步生成音乐（简化方法）: prompt={}", prompt);
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                return generateMusicByPrompt(prompt);
            } catch (IOException e) {
                log.error("异步音乐生成失败（简化方法）: prompt={}, error={}", prompt, e.getMessage(), e);
                throw new RuntimeException("异步音乐生成失败", e);
            }
        });
    }

    @Override
    public Lyria2Output generateMusicWithSeed(String prompt, Integer seed) throws IOException {
        if (prompt == null || prompt.trim().isEmpty()) {
            throw new IllegalArgumentException("提示词不能为空");
        }
        
        Lyria2Input input = Lyria2Input.withSeed(prompt, seed);
        return generateMusic(input);
    }

    @Override
    public Lyria2Output generateMusicWithFullParams(String prompt, Integer seed, String negativePrompt) throws IOException {
        if (prompt == null || prompt.trim().isEmpty()) {
            throw new IllegalArgumentException("提示词不能为空");
        }
        
        Lyria2Input input = Lyria2Input.full(prompt, seed, negativePrompt);
        return generateMusic(input);
    }

    /**
     * 创建音乐生成记录
     *
     * @param input 输入参数
     * @param sessionId 会话ID
     * @param segmentId 章节ID
     * @param userId 用户ID
     * @return 创建的记录
     */
    private AiMusicGenerationRecordPo createMusicGenerationRecord(Lyria2Input input, String sessionId, String segmentId, String userId) {
        try {
            AiMusicGenerationRecordPo record = AiMusicGenerationRecordPo.builder()
                    .sessionId(sessionId)
                    .segmentId(segmentId)
                    .userId(userId)
                    .prompt(input.getPrompt())
                    .negativePrompt(input.getNegativePrompt())
                    .seed(input.getSeed())
                    .status(AiMusicGenerationRecordPo.Status.PENDING)
                    .requestParams(objectMapper.writeValueAsString(input))
                    .createTime(new Date())
                    .updateTime(new Date())
                    .delFlag(0)
                    .build();

            musicGenerationRecordMapper.insert(record);
            log.info("创建音乐生成记录成功: id={}, prompt={}", record.getId(), input.getPrompt());

            return record;

        } catch (JsonProcessingException e) {
            log.error("序列化请求参数失败: {}", e.getMessage(), e);
            throw new RuntimeException("创建音乐生成记录失败", e);
        }
    }

    /**
     * 更新记录的队列状态信息
     *
     * @param record 记录
     * @param status 队列状态
     */
    private void updateRecordWithQueueStatus(AiMusicGenerationRecordPo record, FalQueueStatus status) {
        try {
            record.setRequestId(status.getRequestId());
            record.setStatus(mapQueueStatusToRecordStatus(status.getStatus()));
            record.setQueuePosition(status.getQueuePosition());
            record.setGenerationStartTime(new Date());
            record.setUpdateTime(new Date());

            musicGenerationRecordMapper.updateById(record);
            log.debug("更新音乐生成记录状态成功: id={}, requestId={}, status={}",
                     record.getId(), status.getRequestId(), status.getStatus());

        } catch (Exception e) {
            log.error("更新音乐生成记录状态失败: id={}, error={}", record.getId(), e.getMessage(), e);
        }
    }

    /**
     * 更新记录状态
     *
     * @param recordId 记录ID
     * @param status 状态
     * @param errorMessage 错误信息
     */
    private void updateRecordStatus(Long recordId, String status, String errorMessage) {
        try {
            AiMusicGenerationRecordPo record = musicGenerationRecordMapper.selectById(recordId);
            if (record != null) {
                record.setStatus(status);
                record.setErrorMessage(errorMessage);
                record.setUpdateTime(new Date());

                if (AiMusicGenerationRecordPo.Status.COMPLETED.equals(status) ||
                    AiMusicGenerationRecordPo.Status.FAILED.equals(status)) {
                    record.setGenerationEndTime(new Date());
                    record.calculateCostTime();
                }

                musicGenerationRecordMapper.updateById(record);
                log.debug("更新音乐生成记录状态成功: id={}, status={}", recordId, status);
            }
        } catch (Exception e) {
            log.error("更新音乐生成记录状态失败: id={}, error={}", recordId, e.getMessage(), e);
        }
    }

    /**
     * 更新记录的生成结果
     *
     * @param requestId 请求ID
     * @param output 生成结果
     */
    private void updateRecordWithResult(String requestId, Lyria2Output output) {
        try {
            AiMusicGenerationRecordPo record = musicGenerationRecordMapper.selectByRequestId(requestId);
            if (record != null) {
                record.setStatus(AiMusicGenerationRecordPo.Status.COMPLETED);
                record.setMusicUrl(output.getAudioUrl());
                record.setMusicFileName(output.getAudioFileName());
                record.setMusicFileSize(output.getAudioFileSize() != null ? output.getAudioFileSize().longValue() : null);
                record.setMusicContentType(output.getAudioContentType());
                record.setGenerationEndTime(new Date());
                record.setResponseData(objectMapper.writeValueAsString(output));
                record.setUpdateTime(new Date());
                record.calculateCostTime();

                musicGenerationRecordMapper.updateById(record);
                log.info("更新音乐生成记录结果成功: requestId={}, musicUrl={}", requestId, output.getAudioUrl());
            }
        } catch (Exception e) {
            log.error("更新音乐生成记录结果失败: requestId={}, error={}", requestId, e.getMessage(), e);
        }
    }

    /**
     * 映射队列状态到记录状态
     *
     * @param queueStatus 队列状态
     * @return 记录状态
     */
    private String mapQueueStatusToRecordStatus(FalQueueStatus.Status queueStatus) {
        if (queueStatus == null) {
            return AiMusicGenerationRecordPo.Status.PENDING;
        }

        switch (queueStatus) {
            case IN_QUEUE:
                return AiMusicGenerationRecordPo.Status.IN_QUEUE;
            case IN_PROGRESS:
                return AiMusicGenerationRecordPo.Status.IN_PROGRESS;
            case COMPLETED:
                return AiMusicGenerationRecordPo.Status.COMPLETED;
            default:
                return AiMusicGenerationRecordPo.Status.PENDING;
        }
    }

    // ==================== 数据库查询方法实现 ====================

    @Override
    public AiMusicGenerationRecordPo getMusicGenerationRecord(String requestId) {
        try {
            return musicGenerationRecordMapper.selectByRequestId(requestId);
        } catch (Exception e) {
            log.error("查询音乐生成记录失败: requestId={}, error={}", requestId, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public List<AiMusicGenerationRecordPo> getMusicGenerationRecordsBySession(String sessionId) {
        try {
            return musicGenerationRecordMapper.selectBySessionId(sessionId);
        } catch (Exception e) {
            log.error("查询会话音乐生成记录失败: sessionId={}, error={}", sessionId, e.getMessage(), e);
            return List.of();
        }
    }

    @Override
    public List<AiMusicGenerationRecordPo> getMusicGenerationRecordsBySessionAndSegment(String sessionId, String segmentId) {
        try {
            return musicGenerationRecordMapper.selectBySessionIdAndSegmentId(sessionId, segmentId);
        } catch (Exception e) {
            log.error("查询会话章节音乐生成记录失败: sessionId={}, segmentId={}, error={}",
                     sessionId, segmentId, e.getMessage(), e);
            return List.of();
        }
    }

    @Override
    public List<AiMusicGenerationRecordPo> getMusicGenerationRecordsByUser(String userId, Integer limit) {
        try {
            return musicGenerationRecordMapper.selectByUserId(userId, limit);
        } catch (Exception e) {
            log.error("查询用户音乐生成记录失败: userId={}, error={}", userId, e.getMessage(), e);
            return List.of();
        }
    }

    @Override
    public List<AiMusicGenerationRecordPo> getRecentSuccessRecords(String userId, Integer limit) {
        try {
            return musicGenerationRecordMapper.selectRecentSuccessRecords(userId, limit);
        } catch (Exception e) {
            log.error("查询用户成功音乐生成记录失败: userId={}, error={}", userId, e.getMessage(), e);
            return List.of();
        }
    }

    @Override
    public int countUserGenerations(String userId, String startTime, String endTime) {
        try {
            return musicGenerationRecordMapper.countUserGenerations(userId, startTime, endTime);
        } catch (Exception e) {
            log.error("统计用户音乐生成数量失败: userId={}, error={}", userId, e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public int countProcessingTasks() {
        try {
            return musicGenerationRecordMapper.countProcessingTasks();
        } catch (Exception e) {
            log.error("统计处理中任务数量失败: error={}", e.getMessage(), e);
            return 0;
        }
    }

    @Override
    public int countUserProcessingTasks(String userId) {
        try {
            return musicGenerationRecordMapper.countUserProcessingTasks(userId);
        } catch (Exception e) {
            log.error("统计用户处理中任务数量失败: userId={}, error={}", userId, e.getMessage(), e);
            return 0;
        }
    }
}
