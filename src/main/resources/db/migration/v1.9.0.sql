ALTER TABLE `wl_avatar`.`ai_canvas_shot`
    ADD COLUMN `play_duration` bigint(16) NOT NULL DEFAULT 5000 COMMENT '播放时长（毫秒） 默认5000' AFTER `shot_status`;




-- 创建音乐生成记录表
CREATE TABLE IF NOT EXISTS `ai_music_generation_record` (
                                                            `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                                            `session_id` varchar(64) DEFAULT NULL COMMENT '会话ID',
                                                            `segment_id` varchar(64) DEFAULT NULL COMMENT '章节ID',
                                                            `user_id` varchar(64) NOT NULL COMMENT '用户ID',
                                                            `request_id` varchar(100) NOT NULL COMMENT 'Fal AI请求ID',
                                                            `prompt` text NOT NULL COMMENT '音乐生成提示词',
                                                            `negative_prompt` varchar(1000) DEFAULT 'low quality' COMMENT '负面提示词',
                                                            `seed` int(11) DEFAULT NULL COMMENT '种子值，用于确定性生成',
                                                            `status` varchar(20) NOT NULL DEFAULT 'PENDING' COMMENT '生成状态：PENDING-等待中, IN_QUEUE-队列中, IN_PROGRESS-处理中, COMPLETED-已完成, FAILED-失败, CANCELLED-已取消',
                                                            `queue_position` int(11) DEFAULT NULL COMMENT '队列位置',
                                                            `music_url` varchar(500) DEFAULT NULL COMMENT '生成的音乐文件URL',
                                                            `music_file_name` varchar(255) DEFAULT NULL COMMENT '音乐文件名',
                                                            `music_file_size` bigint(20) DEFAULT NULL COMMENT '音乐文件大小（字节）',
                                                            `music_content_type` varchar(50) DEFAULT NULL COMMENT '音乐文件MIME类型',
                                                            `music_duration` bigint(20) DEFAULT NULL COMMENT '音乐时长（毫秒）',
                                                            `error_message` text COMMENT '错误信息',
                                                            `consume_points` int(11) DEFAULT 0 COMMENT '消耗积分',
                                                            `generation_start_time` datetime DEFAULT NULL COMMENT '生成开始时间',
                                                            `generation_end_time` datetime DEFAULT NULL COMMENT '生成完成时间',
                                                            `generation_cost_time` bigint(20) DEFAULT NULL COMMENT '生成耗时（毫秒）',
                                                            `request_params` text COMMENT '完整请求参数（JSON格式）',
                                                            `response_data` text COMMENT '完整响应数据（JSON格式）',
                                                            `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                            `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                                            `del_flag` tinyint(1) NOT NULL DEFAULT 0 COMMENT '删除标记(0-正常,1-删除)',
                                                            PRIMARY KEY (`id`),
                                                            UNIQUE KEY `uk_request_id` (`request_id`),
                                                            KEY `idx_session_id` (`session_id`),
                                                            KEY `idx_segment_id` (`segment_id`),
                                                            KEY `idx_user_id` (`user_id`),
                                                            KEY `idx_create_time` (`create_time`),
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='音乐生成记录表';


ALTER TABLE `wl_avatar`.`ai_chapter`
    MODIFY COLUMN `prompt` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '章节提示词' AFTER `segment_name`;



ADD COLUMN `has_reference_image` TINYINT(1) DEFAULT 0 COMMENT '是否参考图，0-无参考图，1-有参考图' AFTER `request_id`;
