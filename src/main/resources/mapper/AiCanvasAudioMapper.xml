<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wlink.agent.dao.mapper.AiCanvasAudioMapper">

    <resultMap id="BaseResultMap" type="com.wlink.agent.dao.po.AiCanvasAudioPo">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="canvasId" column="canvas_id" jdbcType="BIGINT"/>
            <result property="shotCode" column="shot_code" jdbcType="VARCHAR"/>
            <result property="audioUrl" column="audio_url" jdbcType="VARCHAR"/>
            <result property="audioType" column="audio_type" jdbcType="TINYINT"/>
            <result property="text" column="text" jdbcType="VARCHAR"/>
            <result property="voiceId" column="voice_id" jdbcType="VARCHAR"/>
            <result property="audioDuration" column="audio_duration" jdbcType="INTEGER"/>
            <result property="sortOrder" column="sort_order" jdbcType="INTEGER"/>
            <result property="volume" column="volume" jdbcType="DECIMAL"/>
            <result property="ttsrecordId" column="tts_record_id" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,canvas_id,shot_code,
        audio_url,audio_type,text,
        voice_id,audio_duration,sort_order,
        volume,tts_record_id,create_time,update_time,del_flag
    </sql>
</mapper>
