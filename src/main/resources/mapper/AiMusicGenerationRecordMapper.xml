<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wlink.agent.dao.mapper.AiMusicGenerationRecordMapper">

    <resultMap id="BaseResultMap" type="com.wlink.agent.dao.po.AiMusicGenerationRecordPo">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="sessionId" column="session_id" jdbcType="VARCHAR"/>
        <result property="segmentId" column="segment_id" jdbcType="VARCHAR"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="requestId" column="request_id" jdbcType="VARCHAR"/>
        <result property="prompt" column="prompt" jdbcType="LONGVARCHAR"/>
        <result property="negativePrompt" column="negative_prompt" jdbcType="VARCHAR"/>
        <result property="seed" column="seed" jdbcType="INTEGER"/>
        <result property="status" column="status" jdbcType="VARCHAR"/>
        <result property="queuePosition" column="queue_position" jdbcType="INTEGER"/>
        <result property="musicUrl" column="music_url" jdbcType="VARCHAR"/>
        <result property="musicFileName" column="music_file_name" jdbcType="VARCHAR"/>
        <result property="musicFileSize" column="music_file_size" jdbcType="BIGINT"/>
        <result property="musicContentType" column="music_content_type" jdbcType="VARCHAR"/>
        <result property="musicDuration" column="music_duration" jdbcType="BIGINT"/>
        <result property="errorMessage" column="error_message" jdbcType="LONGVARCHAR"/>
        <result property="consumePoints" column="consume_points" jdbcType="INTEGER"/>
        <result property="generationStartTime" column="generation_start_time" jdbcType="TIMESTAMP"/>
        <result property="generationEndTime" column="generation_end_time" jdbcType="TIMESTAMP"/>
        <result property="generationCostTime" column="generation_cost_time" jdbcType="BIGINT"/>
        <result property="requestParams" column="request_params" jdbcType="LONGVARCHAR"/>
        <result property="responseData" column="response_data" jdbcType="LONGVARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, session_id, segment_id, user_id, request_id, prompt, negative_prompt, seed,
        status, queue_position, music_url, music_file_name, music_file_size, music_content_type,
        music_duration, error_message, consume_points, generation_start_time, generation_end_time,
        generation_cost_time, request_params, response_data, create_time, update_time, del_flag
    </sql>

    <!-- 根据请求ID查询记录 -->
    <select id="selectByRequestId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ai_music_generation_record
        WHERE request_id = #{requestId}
          AND del_flag = 0
    </select>

    <!-- 根据会话ID查询记录列表 -->
    <select id="selectBySessionId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ai_music_generation_record
        WHERE session_id = #{sessionId}
          AND del_flag = 0
        ORDER BY create_time DESC
    </select>

    <!-- 根据会话ID和章节ID查询记录列表 -->
    <select id="selectBySessionIdAndSegmentId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ai_music_generation_record
        WHERE session_id = #{sessionId}
          AND segment_id = #{segmentId}
          AND del_flag = 0
        ORDER BY create_time DESC
    </select>

    <!-- 根据用户ID查询记录列表 -->
    <select id="selectByUserId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ai_music_generation_record
        WHERE user_id = #{userId}
          AND del_flag = 0
        ORDER BY create_time DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 根据状态查询记录列表 -->
    <select id="selectByStatus" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ai_music_generation_record
        WHERE status = #{status}
          AND del_flag = 0
        ORDER BY create_time ASC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 查询正在处理中的任务数量 -->
    <select id="countProcessingTasks" resultType="int">
        SELECT COUNT(1)
        FROM ai_music_generation_record
        WHERE status IN ('IN_QUEUE', 'IN_PROGRESS')
          AND del_flag = 0
    </select>

    <!-- 查询用户正在处理中的任务数量 -->
    <select id="countUserProcessingTasks" resultType="int">
        SELECT COUNT(1)
        FROM ai_music_generation_record
        WHERE user_id = #{userId}
          AND status IN ('PENDING', 'IN_QUEUE', 'IN_PROGRESS')
          AND del_flag = 0
    </select>

    <!-- 查询超时的处理中任务 -->
    <select id="selectTimeoutProcessingTasks" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ai_music_generation_record
        WHERE status IN ('IN_QUEUE', 'IN_PROGRESS')
          AND create_time &lt; DATE_SUB(NOW(), INTERVAL #{timeoutMinutes} MINUTE)
          AND del_flag = 0
        ORDER BY create_time ASC
    </select>

    <!-- 批量更新任务状态 -->
    <update id="batchUpdateStatus">
        UPDATE ai_music_generation_record
        SET status = #{status},
            update_time = NOW()
            <if test="errorMessage != null">
                , error_message = #{errorMessage}
            </if>
        WHERE request_id IN
        <foreach collection="requestIds" item="requestId" open="(" separator="," close=")">
            #{requestId}
        </foreach>
          AND del_flag = 0
    </update>

    <!-- 统计用户的音乐生成数量 -->
    <select id="countUserGenerations" resultType="int">
        SELECT COUNT(1)
        FROM ai_music_generation_record
        WHERE user_id = #{userId}
          AND del_flag = 0
        <if test="startTime != null">
            AND create_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND create_time &lt;= #{endTime}
        </if>
    </select>

    <!-- 查询最近的成功生成记录 -->
    <select id="selectRecentSuccessRecords" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ai_music_generation_record
        WHERE user_id = #{userId}
          AND status = 'COMPLETED'
          AND music_url IS NOT NULL
          AND del_flag = 0
        ORDER BY create_time DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 根据会话ID删除记录（逻辑删除） -->
    <update id="deleteBySessionId">
        UPDATE ai_music_generation_record
        SET del_flag = 1, update_time = NOW()
        WHERE session_id = #{sessionId}
          AND del_flag = 0
    </update>

    <!-- 根据会话ID和章节ID删除记录（逻辑删除） -->
    <update id="deleteBySessionIdAndSegmentId">
        UPDATE ai_music_generation_record
        SET del_flag = 1, update_time = NOW()
        WHERE session_id = #{sessionId}
          AND segment_id = #{segmentId}
          AND del_flag = 0
    </update>

</mapper>
