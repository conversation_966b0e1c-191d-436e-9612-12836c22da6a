<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wlink.agent.dao.mapper.AiVideoRenderExportMapper">

    <resultMap id="BaseResultMap" type="com.wlink.agent.dao.po.AiVideoRenderExportPo">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="canvasId" column="canvas_id" jdbcType="BIGINT"/>
        <result property="resolution" column="resolution" jdbcType="VARCHAR"/>
        <result property="ratio" column="ratio" jdbcType="VARCHAR"/>
        <result property="fps" column="fps" jdbcType="INTEGER"/>
        <result property="showSubtitle" column="show_subtitle" jdbcType="TINYINT"/>
        <result property="renderTaskId" column="render_task_id" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="TINYINT"/>
        <result property="videoUrl" column="video_url" jdbcType="VARCHAR"/>
        <result property="videoDuration" column="video_duration" jdbcType="BIGINT"/>
        <result property="firstFrameUrl" column="first_frame_url" jdbcType="VARCHAR"/>
        <result property="shareStatus" column="share_status" jdbcType="TINYINT"/>
        <result property="shareCode" column="share_code" jdbcType="VARCHAR"/>
        <result property="shareTime" column="share_time" jdbcType="TIMESTAMP"/>
        <result property="canvasDataJson" column="canvas_data_json" jdbcType="LONGVARCHAR"/>
        <result property="errorMessage" column="error_message" jdbcType="VARCHAR"/>
        <result property="queryFailCount" column="query_fail_count" jdbcType="INTEGER"/>
        <result property="startTime" column="start_time" jdbcType="TIMESTAMP"/>
        <result property="completeTime" column="complete_time" jdbcType="TIMESTAMP"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        e.id, e.user_id, e.canvas_id, e.resolution, e.ratio, e.fps, e.show_subtitle,
        e.render_task_id, e.status, e.video_url, e.video_duration, e.first_frame_url,
        e.share_status, e.share_code, e.share_time, e.canvas_data_json, e.error_message,
        e.query_fail_count, e.start_time, e.complete_time, e.create_time, e.update_time, e.del_flag
    </sql>

    <!-- 根据分享码查询视频导出记录 -->
    <select id="selectByShareCode" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ai_video_render_export e
        WHERE e.share_code = #{shareCode}
          AND e.del_flag = 0
    </select>

    <!-- 分页查询已分享的视频记录（关联画布表） -->
    <select id="selectSharedVideosWithCanvas" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM ai_video_render_export e
        INNER JOIN ai_canvas c ON e.canvas_id = c.id
        WHERE e.share_status = 1
          AND e.del_flag = 0
          AND e.status = 2
          AND e.share_code IS NOT NULL
          AND c.del_flag = 0
        ORDER BY e.share_time DESC
    </select>

</mapper>
