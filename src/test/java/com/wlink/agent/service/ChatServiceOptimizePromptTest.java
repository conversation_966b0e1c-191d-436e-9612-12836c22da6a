package com.wlink.agent.service;

import com.wlink.agent.client.DifyCompletionClient;
import com.wlink.agent.client.model.dify.DifyCompletionResponse;
import com.wlink.agent.model.dto.SimpleUserInfo;
import com.wlink.agent.model.res.PromptOptimizeRes;
import com.wlink.agent.service.impl.ChatServiceImpl;
import com.wlink.agent.utils.UserContext;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;

/**
 * ChatService提示词优化功能测试类
 */
@ExtendWith(MockitoExtension.class)
class ChatServiceOptimizePromptTest {

    @Mock
    private DifyCompletionClient difyCompletionClient;

    private ChatServiceImpl chatService;

    @BeforeEach
    void setUp() {
        chatService = new ChatServiceImpl();
        // 使用反射注入mock对象
        ReflectionTestUtils.setField(chatService, "difyCompletionClient", difyCompletionClient);
    }

    @Test
    void testOptimizePrompt_Success() throws IOException {
        // 准备测试数据
        String originalPrompt = "生成一个游戏广告";
        String user = "test-user";
        String optimizedAnswer = "风格化3D卡通画面，色彩明亮，皮克斯动画电影质感。我需要一个抖音投放的快节奏游戏广告，一个部落在雪地中通过采集材料在雪地中搭建篝火取暖，搭建家园，抵御其他部落入侵。有旁白在迅速解说。不要音效。";

        DifyCompletionResponse mockResponse = DifyCompletionResponse.builder()
                .event("message")
                .taskId("test-task-id")
                .id("test-id")
                .messageId("test-message-id")
                .mode("completion")
                .answer(optimizedAnswer)
                .createdAt(System.currentTimeMillis())
                .build();

        // Mock UserContext
        SimpleUserInfo mockUser = new SimpleUserInfo();
        mockUser.setUserId(user);

        try (MockedStatic<UserContext> mockedUserContext = mockStatic(UserContext.class)) {
            mockedUserContext.when(UserContext::getUser).thenReturn(mockUser);

            // 配置mock
            when(difyCompletionClient.requestCompletion(originalPrompt, user, null)).thenReturn(mockResponse);

            // 执行测试
            PromptOptimizeRes result = chatService.optimizePrompt(originalPrompt);

            // 验证结果
            assertNotNull(result);
            assertEquals(optimizedAnswer, result.getOptimizedPrompt());
        }
    }

    @Test
    void testOptimizePromptWithImages_Success() throws IOException {
        // 准备测试数据
        String originalPrompt = "根据图中的人物，创作一个故事";
        String user = "test-user";
        List<String> imageUrls = Arrays.asList(
                "https://wlpaas.weilitech.cn/dify/prod/1927918453497950208/image/98e6e550a81441d58e42b6b68f8ba0e4.png"
        );
        String optimizedAnswer = "根据图片中的角色特征，创作了一个关于勇敢冒险者的精彩故事...";

        DifyCompletionResponse mockResponse = DifyCompletionResponse.builder()
                .event("message")
                .taskId("test-task-id")
                .id("test-id")
                .messageId("test-message-id")
                .mode("completion")
                .answer(optimizedAnswer)
                .createdAt(System.currentTimeMillis())
                .build();

        // Mock UserContext
        SimpleUserInfo mockUser = new SimpleUserInfo();
        mockUser.setUserId(user);

        try (MockedStatic<UserContext> mockedUserContext = mockStatic(UserContext.class)) {
            mockedUserContext.when(UserContext::getUser).thenReturn(mockUser);

            // 配置mock
            when(difyCompletionClient.requestCompletion(originalPrompt, user, imageUrls)).thenReturn(mockResponse);

            // 执行测试
            PromptOptimizeRes result = chatService.optimizePrompt(originalPrompt, imageUrls);

            // 验证结果
            assertNotNull(result);
            assertEquals(optimizedAnswer, result.getOptimizedPrompt());
        }
    }

    @Test
    void testOptimizePrompt_NullResponse() throws IOException {
        // 准备测试数据
        String originalPrompt = "生成一个游戏广告";
        String user = "test-user";

        // Mock UserContext
        SimpleUserInfo mockUser = new SimpleUserInfo();
        mockUser.setUserId(user);

        try (MockedStatic<UserContext> mockedUserContext = mockStatic(UserContext.class)) {
            mockedUserContext.when(UserContext::getUser).thenReturn(mockUser);

            // 配置mock返回null
            when(difyCompletionClient.requestCompletion(originalPrompt, user, null)).thenReturn(null);

            // 执行测试
            PromptOptimizeRes result = chatService.optimizePrompt(originalPrompt);

            // 验证结果
            assertNotNull(result);
            assertNull(result.getOptimizedPrompt());
        }
    }

    @Test
    void testOptimizePrompt_IOException() throws IOException {
        // 准备测试数据
        String originalPrompt = "生成一个游戏广告";
        String user = "test-user";

        // Mock UserContext
        SimpleUserInfo mockUser = new SimpleUserInfo();
        mockUser.setUserId(user);

        try (MockedStatic<UserContext> mockedUserContext = mockStatic(UserContext.class)) {
            mockedUserContext.when(UserContext::getUser).thenReturn(mockUser);

            // 配置mock抛出异常
            when(difyCompletionClient.requestCompletion(originalPrompt, user, null)).thenThrow(new IOException("Network error"));

            // 执行测试
            PromptOptimizeRes result = chatService.optimizePrompt(originalPrompt);

            // 验证结果
            assertNotNull(result);
            assertNull(result.getOptimizedPrompt());
        }
    }
}
